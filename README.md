# 易宿管 - 智能宿舍管理系统

一个基于 React + TypeScript + Node.js + MySQL 的现代化宿舍管理系统，提供完整的宿舍管理解决方案。

## 🚀 项目特色

- **多角色管理**: 系统管理员、宿舍管理员、学生、维修人员
- **完整功能**: 用户管理、宿舍分配、维修请求、公告管理、水电费管理等
- **现代化UI**: 响应式设计，美观的用户界面
- **实时数据**: 前后端分离，实时数据同步
- **安全认证**: JWT认证，角色权限控制

## 📋 系统功能

### 系统管理员
- 用户管理（增删改查）
- 学院、专业管理
- 宿舍楼管理
- 系统公告发布

### 宿舍管理员
- 房间管理
- 学生分配
- 违规记录
- 晚归记录
- 访客管理
- 文明宿舍评分

### 学生
- 个人信息管理
- 维修请求提交
- 水电费查询
- 公告查看

### 维修人员
- 维修任务处理
- 维修进度更新
- 维修记录查看

## 🛠️ 技术栈

### 前端
- React 19.1.0
- TypeScript
- React Router DOM
- Vite
- Tailwind CSS

### 后端
- Node.js
- Express.js
- MySQL 8.0
- JWT认证
- CORS支持

## 📦 快速开始

### 环境要求
- Node.js 16.0+
- MySQL 8.0+
- Git

### 一键启动（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd yisuguan
```

2. **运行启动脚本**
```bash
# Windows
start-project.bat

# 或者手动执行以下步骤
```

### 手动启动

1. **导入数据库**
```bash
# 确保MySQL服务已启动，用户名root，密码root
mysql -u root -proot < database.sql
```

2. **启动后端服务**
```bash
cd backend
npm install
npm start
```

3. **启动前端服务**
```bash
cd qian
npm install
npm run dev
```

## 🌐 访问地址

- **前端**: http://localhost:5173
- **后端**: http://localhost:3001
- **API文档**: [backend/API_DOCUMENTATION.md](backend/API_DOCUMENTATION.md)

## 👤 默认账号

| 角色 | 邮箱 | 密码 | 说明 |
|------|------|------|------|
| 系统管理员 | <EMAIL> | password123 | 全系统管理权限 |
| 宿舍管理员 | <EMAIL> | password123 | A栋管理员 |
| 宿舍管理员 | <EMAIL> | password123 | B栋管理员 |
| 学生 | <EMAIL> | password123 | A栋101王五 |
| 学生 | <EMAIL> | password123 | B栋205赵六 |
| 学生 | <EMAIL> | password123 | A栋102孙七 |
| 维修人员 | <EMAIL> | password123 | 维修工丁师傅 |

## 📁 项目结构

```
yisuguan/
├── qian/                    # 前端项目
│   ├── src/
│   │   ├── components/      # 通用组件
│   │   ├── contexts/        # React Context
│   │   ├── pages/          # 页面组件
│   │   ├── api/            # API配置
│   │   └── types.ts        # TypeScript类型定义
│   ├── package.json
│   └── vite.config.ts
├── backend/                 # 后端项目
│   ├── controllers/         # 控制器
│   ├── routes/             # 路由
│   ├── middleware/         # 中间件
│   ├── config/             # 配置文件
│   ├── utils/              # 工具函数
│   ├── package.json
│   └── server.js
├── database.sql            # 数据库结构和初始数据
├── import-database.bat     # 数据库导入脚本
├── start-backend.bat       # 后端启动脚本
├── start-project.bat       # 一键启动脚本
└── README.md
```

## 🔧 开发指南

### 环境配置

1. **数据库配置** (backend/.env)
```env
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=root
DB_NAME=redhat
DB_PORT=3306
```

2. **JWT配置**
```env
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h
```

### API开发

1. 在 `backend/controllers/` 创建控制器
2. 在 `backend/routes/` 创建路由
3. 在 `backend/server.js` 注册路由

### 前端开发

1. 在 `qian/pages/` 创建页面组件
2. 在 `qian/types.ts` 添加类型定义
3. 在 `App.tsx` 添加路由

## 🐛 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查MySQL服务是否启动
   - 确认用户名密码为root/root
   - 检查端口3306是否被占用

2. **前端无法访问后端**
   - 确认后端服务在3001端口运行
   - 检查CORS配置
   - 确认API_BASE_URL配置正确

3. **登录失败**
   - 确认数据库已正确导入
   - 检查JWT_SECRET配置
   - 使用默认账号测试

### 日志查看

- **后端日志**: 控制台输出
- **前端日志**: 浏览器开发者工具Console
- **数据库日志**: MySQL错误日志

## 📚 API文档

详细的API文档请查看: [backend/API_DOCUMENTATION.md](backend/API_DOCUMENTATION.md)

### 主要API端点

- `POST /api/auth/login` - 用户登录
- `GET /api/users` - 获取用户列表 (管理员)
- `GET /api/repairs` - 获取维修请求
- `POST /api/repairs` - 创建维修请求 (学生)
- `GET /api/announcements` - 获取公告列表

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>

---

© 2024 易宿管 - 智能宿舍管理系统
