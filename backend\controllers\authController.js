import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { pool } from '../config/database.js';
import { successResponse, errorResponse } from '../utils/response.js';

// 生成JWT令牌
const generateToken = (userId) => {
  return jwt.sign(
    { userId },
    process.env.JWT_SECRET,
    { expiresIn: process.env.JWT_EXPIRES_IN || '24h' }
  );
};

// 用户登录
export const login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // 验证输入
    if (!email || !password) {
      return errorResponse(res, '邮箱和密码不能为空', 400);
    }

    // 查找用户
    const [rows] = await pool.execute(
      `SELECT u.*, c.name as college_name, m.name as major_name, db.name as dorm_building_name 
       FROM users u 
       LEFT JOIN colleges c ON u.college_id = c.id 
       LEFT JOIN majors m ON u.major_id = m.id 
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id 
       WHERE u.email = ?`,
      [email]
    );

    if (rows.length === 0) {
      return errorResponse(res, '邮箱或密码错误', 401);
    }

    const user = rows[0];

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return errorResponse(res, '邮箱或密码错误', 401);
    }

    // 生成令牌
    const token = generateToken(user.id);

    // 返回用户信息（不包含密码）
    const userInfo = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      phone: user.phone,
      college: user.college_name,
      major: user.major_name,
      dormBuilding: user.dorm_building_name,
      roomNumber: user.room_number,
      emergencyContactName: user.emergency_contact_name,
      emergencyContactPhone: user.emergency_contact_phone
    };

    successResponse(res, {
      user: userInfo,
      token
    }, '登录成功');

  } catch (error) {
    console.error('登录错误:', error);
    errorResponse(res, '登录失败，请重试', 500, error);
  }
};

// 用户注册
export const register = async (req, res) => {
  try {
    const {
      name,
      email,
      password,
      role,
      phone,
      college_id,
      major_id,
      dorm_building_id,
      room_number,
      emergency_contact_name,
      emergency_contact_phone
    } = req.body;

    // 验证必填字段
    if (!name || !email || !password || !role) {
      return errorResponse(res, '姓名、邮箱、密码和角色不能为空', 400);
    }

    // 检查邮箱是否已存在
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return errorResponse(res, '该邮箱已被注册', 409);
    }

    // 加密密码
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 生成用户ID
    const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 插入用户
    await pool.execute(
      `INSERT INTO users (id, name, email, password, role, phone, college_id, major_id, 
       dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId, name, email, hashedPassword, role, phone, college_id, major_id,
        dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone
      ]
    );

    successResponse(res, { userId }, '注册成功', 201);

  } catch (error) {
    console.error('注册错误:', error);
    errorResponse(res, '注册失败，请重试', 500, error);
  }
};

// 获取当前用户信息
export const getCurrentUser = async (req, res) => {
  try {
    const userId = req.user.id;

    const [rows] = await pool.execute(
      `SELECT u.*, c.name as college_name, m.name as major_name, db.name as dorm_building_name 
       FROM users u 
       LEFT JOIN colleges c ON u.college_id = c.id 
       LEFT JOIN majors m ON u.major_id = m.id 
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id 
       WHERE u.id = ?`,
      [userId]
    );

    if (rows.length === 0) {
      return errorResponse(res, '用户不存在', 404);
    }

    const user = rows[0];
    const userInfo = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role,
      phone: user.phone,
      college: user.college_name,
      major: user.major_name,
      dormBuilding: user.dorm_building_name,
      roomNumber: user.room_number,
      emergencyContactName: user.emergency_contact_name,
      emergencyContactPhone: user.emergency_contact_phone
    };

    successResponse(res, { user: userInfo });

  } catch (error) {
    console.error('获取用户信息错误:', error);
    errorResponse(res, '获取用户信息失败', 500, error);
  }
};

// 修改密码
export const changePassword = async (req, res) => {
  try {
    const { currentPassword, newPassword } = req.body;
    const userId = req.user.id;

    if (!currentPassword || !newPassword) {
      return errorResponse(res, '当前密码和新密码不能为空', 400);
    }

    // 获取用户当前密码
    const [rows] = await pool.execute(
      'SELECT password FROM users WHERE id = ?',
      [userId]
    );

    if (rows.length === 0) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 验证当前密码
    const isCurrentPasswordValid = await bcrypt.compare(currentPassword, rows[0].password);
    if (!isCurrentPasswordValid) {
      return errorResponse(res, '当前密码错误', 400);
    }

    // 加密新密码
    const saltRounds = 10;
    const hashedNewPassword = await bcrypt.hash(newPassword, saltRounds);

    // 更新密码
    await pool.execute(
      'UPDATE users SET password = ? WHERE id = ?',
      [hashedNewPassword, userId]
    );

    successResponse(res, null, '密码修改成功');

  } catch (error) {
    console.error('修改密码错误:', error);
    errorResponse(res, '修改密码失败', 500, error);
  }
};
