@echo off
echo ========================================
echo 易宿管 - 后端服务启动脚本
echo ========================================
echo.

cd backend

echo 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Node.js，请先安装Node.js
    pause
    exit /b 1
)

echo ✅ Node.js环境正常
echo.

echo 检查依赖包...
if not exist node_modules (
    echo 正在安装依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 依赖包安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖包安装完成
) else (
    echo ✅ 依赖包已存在
)
echo.

echo 启动后端服务...
echo 服务地址: http://localhost:3001
echo 按 Ctrl+C 停止服务
echo.

npm start
