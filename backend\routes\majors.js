import express from 'express';
import {
  getAllMajors,
  getMajorById,
  createMajor,
  updateMajor,
  deleteMajor,
  getMajorsByCollege
} from '../controllers/majorController.js';
import { authenticateToken, requireSystemAdmin } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取所有专业 - 所有用户都可以访问
router.get('/', getAllMajors);

// 根据学院ID获取专业列表 - 所有用户都可以访问
router.get('/college/:college_id', getMajorsByCollege);

// 根据ID获取专业 - 所有用户都可以访问
router.get('/:id', getMajorById);

// 创建专业 - 仅系统管理员
router.post('/', requireSystemAdmin, createMajor);

// 更新专业 - 仅系统管理员
router.put('/:id', requireSystemAdmin, updateMajor);

// 删除专业 - 仅系统管理员
router.delete('/:id', requireSystemAdmin, deleteMajor);

export default router;
