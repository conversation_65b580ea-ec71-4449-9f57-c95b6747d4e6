import express from 'express';
import {
  getAllColleges,
  getCollegeById,
  createCollege,
  updateCollege,
  deleteCollege,
  getCollegeMajors
} from '../controllers/collegeController.js';
import { authenticateToken, requireSystemAdmin } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取所有学院 - 所有用户都可以访问
router.get('/', getAllColleges);

// 获取学院下的专业 - 所有用户都可以访问
router.get('/:id/majors', getCollegeMajors);

// 根据ID获取学院 - 所有用户都可以访问
router.get('/:id', getCollegeById);

// 创建学院 - 仅系统管理员
router.post('/', requireSystemAdmin, createCollege);

// 更新学院 - 仅系统管理员
router.put('/:id', requireSystemAdmin, updateCollege);

// 删除学院 - 仅系统管理员
router.delete('/:id', requireSystemAdmin, deleteCollege);

export default router;
