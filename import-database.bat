@echo off
echo ========================================
echo 易宿管 - 数据库导入脚本
echo ========================================
echo.

echo 正在导入数据库...
echo 请确保MySQL服务已启动，并且root用户密码为root
echo.

mysql -u root -proot < database.sql

if %errorlevel% equ 0 (
    echo.
    echo ✅ 数据库导入成功！
    echo.
    echo 数据库信息：
    echo - 数据库名: redhat
    echo - 用户名: root
    echo - 密码: root
    echo.
    echo 默认登录账号：
    echo 1. 系统管理员: <EMAIL> / password123
    echo 2. 宿舍管理员A栋: <EMAIL> / password123
    echo 3. 宿舍管理员B栋: <EMAIL> / password123
    echo 4. 学生王五: <EMAIL> / password123
    echo 5. 维修人员: <EMAIL> / password123
    echo.
) else (
    echo.
    echo ❌ 数据库导入失败！
    echo 请检查：
    echo 1. MySQL服务是否已启动
    echo 2. root用户密码是否为root
    echo 3. database.sql文件是否存在
    echo.
)

pause
