import express from 'express';
import {
  getAllRooms,
  getRoomById,
  createRoom,
  updateRoom,
  deleteRoom,
  getRoomBeds,
  assignBed,
  releaseBed
} from '../controllers/roomController.js';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取所有房间 - 根据用户角色过滤
router.get('/', getAllRooms);

// 获取房间的床位列表 - 所有用户都可以访问
router.get('/:id/beds', getRoomBeds);

// 根据ID获取房间 - 所有用户都可以访问
router.get('/:id', getRoomById);

// 创建房间 - 仅管理员
router.post('/', requireAdmin, createRoom);

// 更新房间 - 仅管理员
router.put('/:id', requireAdmin, updateRoom);

// 删除房间 - 仅管理员
router.delete('/:id', requireAdmin, deleteRoom);

// 分配床位 - 仅管理员
router.put('/beds/:bedId/assign', requireAdmin, assignBed);

// 释放床位 - 仅管理员
router.put('/beds/:bedId/release', requireAdmin, releaseBed);

export default router;
