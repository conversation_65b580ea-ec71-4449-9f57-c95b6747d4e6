import React, { createContext, useState, useContext, ReactNode, useCallback } from 'react';
import { User, UserRole } from '../types';
import { apiPost, apiGet, API_ENDPOINTS } from '../api/config';

interface AuthContextType {
  currentUser: User | null;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, role: UserRole, password?: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const login = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const response = await apiPost(API_ENDPOINTS.AUTH.LOGIN, { email, password });

      if (response.success) {
        const { user, token } = response.data;
        setCurrentUser(user);
        localStorage.setItem('user', JSON.stringify(user));
        localStorage.setItem('token', token);
      } else {
        throw new Error(response.message || '登录失败');
      }
    } catch (error: any) {
      console.error('登录错误:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const register = useCallback(async (name: string, email: string, role: UserRole, password?: string) => {
    setIsLoading(true);
    try {
      const response = await apiPost(API_ENDPOINTS.AUTH.REGISTER, {
        name,
        email,
        role,
        password: password || 'password123'
      });

      if (response.success) {
        // 注册成功后自动登录
        await login(email, password || 'password123');
      } else {
        throw new Error(response.message || '注册失败');
      }
    } catch (error: any) {
      console.error('注册错误:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [login]);

  const logout = useCallback(() => {
    setCurrentUser(null);
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  }, []);

  // 初始化时从localStorage恢复用户状态
  React.useEffect(() => {
    const savedUser = localStorage.getItem('user');
    const savedToken = localStorage.getItem('token');

    if (savedUser && savedToken) {
      try {
        const user = JSON.parse(savedUser);
        setCurrentUser(user);

        // 验证token是否有效
        apiGet(API_ENDPOINTS.AUTH.ME)
          .then(response => {
            if (response.success) {
              setCurrentUser(response.data.user);
            } else {
              logout();
            }
          })
          .catch(() => {
            logout();
          });
      } catch (error) {
        console.error('解析保存的用户信息失败:', error);
        logout();
      }
    }
  }, []);

  return (
    <AuthContext.Provider value={{ currentUser, login, register, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};