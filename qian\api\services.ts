// API服务文件
import { apiGet, apiPost, apiPut, apiDelete, API_ENDPOINTS } from './config';
import { User, College, Major, DormBuilding, RepairRequest, Announcement, UtilityBill } from '../types';

// 用户相关API
export const userService = {
  // 获取所有用户
  getAllUsers: async () => {
    const response = await apiGet(API_ENDPOINTS.USERS.BASE);
    return response.data.users;
  },

  // 获取用户详情
  getUserById: async (id: string) => {
    const response = await apiGet(`${API_ENDPOINTS.USERS.BASE}/${id}`);
    return response.data.user;
  },

  // 创建用户
  createUser: async (userData: Partial<User>) => {
    const response = await apiPost(API_ENDPOINTS.USERS.BASE, userData);
    return response.data;
  },

  // 更新用户
  updateUser: async (id: string, userData: Partial<User>) => {
    const response = await apiPut(`${API_ENDPOINTS.USERS.BASE}/${id}`, userData);
    return response.data;
  },

  // 删除用户
  deleteUser: async (id: string) => {
    const response = await apiDelete(`${API_ENDPOINTS.USERS.BASE}/${id}`);
    return response.data;
  },

  // 获取宿舍管理员列表
  getDormAdmins: async () => {
    const response = await apiGet(API_ENDPOINTS.USERS.DORM_ADMINS);
    return response.data.dormAdmins;
  },

  // 获取维修人员列表
  getRepairStaff: async () => {
    const response = await apiGet(API_ENDPOINTS.USERS.REPAIR_STAFF);
    return response.data.repairStaff;
  }
};

// 学院相关API
export const collegeService = {
  // 获取所有学院
  getAllColleges: async () => {
    const response = await apiGet(API_ENDPOINTS.COLLEGES.BASE);
    return response.data.colleges;
  },

  // 创建学院
  createCollege: async (collegeData: Partial<College>) => {
    const response = await apiPost(API_ENDPOINTS.COLLEGES.BASE, collegeData);
    return response.data;
  },

  // 更新学院
  updateCollege: async (id: string, collegeData: Partial<College>) => {
    const response = await apiPut(`${API_ENDPOINTS.COLLEGES.BASE}/${id}`, collegeData);
    return response.data;
  },

  // 删除学院
  deleteCollege: async (id: string) => {
    const response = await apiDelete(`${API_ENDPOINTS.COLLEGES.BASE}/${id}`);
    return response.data;
  }
};

// 专业相关API
export const majorService = {
  // 获取所有专业
  getAllMajors: async () => {
    const response = await apiGet(API_ENDPOINTS.MAJORS.BASE);
    return response.data.majors;
  },

  // 根据学院获取专业
  getMajorsByCollege: async (collegeId: string) => {
    const response = await apiGet(`${API_ENDPOINTS.MAJORS.BY_COLLEGE}/${collegeId}`);
    return response.data.majors;
  },

  // 创建专业
  createMajor: async (majorData: Partial<Major>) => {
    const response = await apiPost(API_ENDPOINTS.MAJORS.BASE, majorData);
    return response.data;
  },

  // 更新专业
  updateMajor: async (id: string, majorData: Partial<Major>) => {
    const response = await apiPut(`${API_ENDPOINTS.MAJORS.BASE}/${id}`, majorData);
    return response.data;
  },

  // 删除专业
  deleteMajor: async (id: string) => {
    const response = await apiDelete(`${API_ENDPOINTS.MAJORS.BASE}/${id}`);
    return response.data;
  }
};

// 宿舍楼相关API
export const dormBuildingService = {
  // 获取所有宿舍楼
  getAllDormBuildings: async () => {
    const response = await apiGet(API_ENDPOINTS.DORM_BUILDINGS.BASE);
    return response.data.dormBuildings;
  },

  // 创建宿舍楼
  createDormBuilding: async (buildingData: Partial<DormBuilding>) => {
    const response = await apiPost(API_ENDPOINTS.DORM_BUILDINGS.BASE, buildingData);
    return response.data;
  },

  // 更新宿舍楼
  updateDormBuilding: async (id: string, buildingData: Partial<DormBuilding>) => {
    const response = await apiPut(`${API_ENDPOINTS.DORM_BUILDINGS.BASE}/${id}`, buildingData);
    return response.data;
  },

  // 删除宿舍楼
  deleteDormBuilding: async (id: string) => {
    const response = await apiDelete(`${API_ENDPOINTS.DORM_BUILDINGS.BASE}/${id}`);
    return response.data;
  }
};

// 维修请求相关API
export const repairService = {
  // 获取维修请求列表
  getAllRepairRequests: async (params?: { status?: string; studentId?: string; assignedTo?: string }) => {
    const queryString = params ? '?' + new URLSearchParams(params).toString() : '';
    const response = await apiGet(`${API_ENDPOINTS.REPAIRS.BASE}${queryString}`);
    return response.data.repairRequests;
  },

  // 获取维修请求详情
  getRepairRequestById: async (id: string) => {
    const response = await apiGet(`${API_ENDPOINTS.REPAIRS.BASE}/${id}`);
    return response.data.repairRequest;
  },

  // 创建维修请求
  createRepairRequest: async (repairData: Partial<RepairRequest>) => {
    const response = await apiPost(API_ENDPOINTS.REPAIRS.BASE, repairData);
    return response.data;
  },

  // 更新维修请求状态
  updateRepairRequestStatus: async (id: string, statusData: { status?: string; notes?: string; assignedTo?: string; assignedToName?: string }) => {
    const response = await apiPut(`${API_ENDPOINTS.REPAIRS.BASE}/${id}/status`, statusData);
    return response.data;
  },

  // 指派维修请求
  assignRepairRequest: async (id: string, assignedTo: string) => {
    const response = await apiPut(`${API_ENDPOINTS.REPAIRS.BASE}/${id}/assign`, { assignedTo });
    return response.data;
  },

  // 删除维修请求
  deleteRepairRequest: async (id: string) => {
    const response = await apiDelete(`${API_ENDPOINTS.REPAIRS.BASE}/${id}`);
    return response.data;
  }
};

// 公告相关API
export const announcementService = {
  // 获取公告列表
  getAllAnnouncements: async (params?: { scope?: string; limit?: number }) => {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    const response = await apiGet(`${API_ENDPOINTS.ANNOUNCEMENTS.BASE}${queryString}`);
    return response.data.announcements;
  },

  // 获取公告详情
  getAnnouncementById: async (id: string) => {
    const response = await apiGet(`${API_ENDPOINTS.ANNOUNCEMENTS.BASE}/${id}`);
    return response.data.announcement;
  },

  // 创建公告
  createAnnouncement: async (announcementData: Partial<Announcement>) => {
    const response = await apiPost(API_ENDPOINTS.ANNOUNCEMENTS.BASE, announcementData);
    return response.data;
  },

  // 更新公告
  updateAnnouncement: async (id: string, announcementData: Partial<Announcement>) => {
    const response = await apiPut(`${API_ENDPOINTS.ANNOUNCEMENTS.BASE}/${id}`, announcementData);
    return response.data;
  },

  // 删除公告
  deleteAnnouncement: async (id: string) => {
    const response = await apiDelete(`${API_ENDPOINTS.ANNOUNCEMENTS.BASE}/${id}`);
    return response.data;
  }
};

// 水电费相关API
export const utilityBillService = {
  // 获取水电费列表
  getAllUtilityBills: async (params?: { month?: string; isPaid?: boolean; studentId?: string }) => {
    const queryString = params ? '?' + new URLSearchParams(params as any).toString() : '';
    const response = await apiGet(`/utility-bills${queryString}`);
    return response.data.utilityBills;
  },

  // 获取水电费详情
  getUtilityBillById: async (id: string) => {
    const response = await apiGet(`/utility-bills/${id}`);
    return response.data.utilityBill;
  },

  // 创建水电费记录
  createUtilityBill: async (billData: Partial<UtilityBill>) => {
    const response = await apiPost('/utility-bills', billData);
    return response.data;
  },

  // 更新水电费记录
  updateUtilityBill: async (id: string, billData: Partial<UtilityBill>) => {
    const response = await apiPut(`/utility-bills/${id}`, billData);
    return response.data;
  },

  // 标记为已支付
  markAsPaid: async (id: string) => {
    const response = await apiPut(`/utility-bills/${id}/pay`, {});
    return response.data;
  },

  // 获取学生水电费统计
  getUtilityBillStats: async (studentId: string) => {
    const response = await apiGet(`/utility-bills/stats/${studentId}`);
    return response.data.stats;
  }
};

// 房间相关API
export const roomService = {
  // 获取所有房间
  getAllRooms: async () => {
    const response = await apiGet('/rooms');
    return response.data.rooms;
  },

  // 获取房间床位
  getRoomBeds: async (roomId: string) => {
    const response = await apiGet(`/rooms/${roomId}/beds`);
    return response.data.beds;
  },

  // 创建房间
  createRoom: async (roomData: any) => {
    const response = await apiPost('/rooms', roomData);
    return response.data;
  },

  // 更新房间
  updateRoom: async (id: string, roomData: any) => {
    const response = await apiPut(`/rooms/${id}`, roomData);
    return response.data;
  },

  // 删除房间
  deleteRoom: async (id: string) => {
    const response = await apiDelete(`/rooms/${id}`);
    return response.data;
  },

  // 分配床位
  assignBed: async (bedId: string, studentId: string) => {
    const response = await apiPut(`/rooms/beds/${bedId}/assign`, { studentId });
    return response.data;
  },

  // 释放床位
  releaseBed: async (bedId: string) => {
    const response = await apiPut(`/rooms/beds/${bedId}/release`, {});
    return response.data;
  }
};
