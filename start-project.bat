@echo off
echo ========================================
echo 易宿管 - 完整项目启动脚本
echo ========================================
echo.

echo 第一步：导入数据库
echo ========================================
call import-database.bat
if %errorlevel% neq 0 (
    echo ❌ 数据库导入失败，请检查MySQL服务
    pause
    exit /b 1
)

echo.
echo 第二步：启动后端服务
echo ========================================
start "后端服务" cmd /k "start-backend.bat"

echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

echo.
echo 第三步：启动前端服务
echo ========================================
cd qian
echo 检查前端依赖...
if not exist node_modules (
    echo 正在安装前端依赖...
    call npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
)

echo 启动前端开发服务器...
start "前端服务" cmd /k "npm run dev"

echo.
echo ========================================
echo ✅ 项目启动完成！
echo.
echo 服务地址：
echo - 前端: http://localhost:5173
echo - 后端: http://localhost:3001
echo.
echo 默认登录账号：
echo 1. 系统管理员: <EMAIL> / password123
echo 2. 宿舍管理员A栋: <EMAIL> / password123
echo 3. 宿舍管理员B栋: <EMAIL> / password123
echo 4. 学生王五: <EMAIL> / password123
echo 5. 维修人员: <EMAIL> / password123
echo.
echo 按任意键关闭此窗口...
pause >nul
