import express from 'express';
import {
  getAllUtilityBills,
  getUtilityBillById,
  createUtilityBill,
  updateUtilityBill,
  deleteUtilityBill,
  markAsPaid,
  getUtilityBillStats
} from '../controllers/utilityBillController.js';
import { authenticateToken, requireAdmin, requireSystemAdmin } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取水电费列表 - 根据用户角色过滤
router.get('/', getAllUtilityBills);

// 获取学生的水电费统计 - 学生本人或管理员
router.get('/stats/:studentId', getUtilityBillStats);

// 根据ID获取水电费 - 根据用户角色过滤
router.get('/:id', getUtilityBillById);

// 创建水电费记录 - 仅管理员
router.post('/', requireAdmin, createUtilityBill);

// 更新水电费记录 - 仅管理员
router.put('/:id', requireAdmin, updateUtilityBill);

// 标记为已支付 - 学生本人或管理员
router.put('/:id/pay', markAsPaid);

// 删除水电费记录 - 仅系统管理员
router.delete('/:id', requireSystemAdmin, deleteUtilityBill);

export default router;
