import bcrypt from 'bcryptjs';
import { pool } from '../config/database.js';
import { successResponse, errorResponse } from '../utils/response.js';

// 获取所有用户
export const getAllUsers = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT u.id, u.name, u.email, u.role, u.phone, u.room_number,
              u.emergency_contact_name, u.emergency_contact_phone,
              c.name as college, m.name as major, db.name as dormBuilding
       FROM users u 
       LEFT JOIN colleges c ON u.college_id = c.id 
       LEFT JOIN majors m ON u.major_id = m.id 
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id 
       ORDER BY u.created_at DESC`
    );

    successResponse(res, { users: rows });
  } catch (error) {
    console.error('获取用户列表错误:', error);
    errorResponse(res, '获取用户列表失败', 500, error);
  }
};

// 根据ID获取用户
export const getUserById = async (req, res) => {
  try {
    const { id } = req.params;

    const [rows] = await pool.execute(
      `SELECT u.*, c.name as college_name, m.name as major_name, db.name as dorm_building_name 
       FROM users u 
       LEFT JOIN colleges c ON u.college_id = c.id 
       LEFT JOIN majors m ON u.major_id = m.id 
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id 
       WHERE u.id = ?`,
      [id]
    );

    if (rows.length === 0) {
      return errorResponse(res, '用户不存在', 404);
    }

    const user = rows[0];
    // 不返回密码
    delete user.password;

    successResponse(res, { user });
  } catch (error) {
    console.error('获取用户详情错误:', error);
    errorResponse(res, '获取用户详情失败', 500, error);
  }
};

// 创建用户
export const createUser = async (req, res) => {
  try {
    const {
      name,
      email,
      password,
      role,
      phone,
      college,
      major,
      dorm_building_id,
      room_number,
      emergency_contact_name,
      emergency_contact_phone
    } = req.body;

    // 验证必填字段
    if (!name || !email || !password || !role) {
      return errorResponse(res, '姓名、邮箱、密码和角色不能为空', 400);
    }

    // 检查邮箱是否已存在
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return errorResponse(res, '该邮箱已被注册', 409);
    }

    // 获取学院和专业ID
    let college_id = null, major_id = null;
    
    if (college) {
      const [collegeRows] = await pool.execute('SELECT id FROM colleges WHERE name = ?', [college]);
      if (collegeRows.length > 0) {
        college_id = collegeRows[0].id;
      }
    }
    
    if (major) {
      const [majorRows] = await pool.execute('SELECT id FROM majors WHERE name = ?', [major]);
      if (majorRows.length > 0) {
        major_id = majorRows[0].id;
      }
    }

    // 加密密码
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 生成用户ID
    const userId = 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 插入用户
    await pool.execute(
      `INSERT INTO users (id, name, email, password, role, phone, college_id, major_id, 
       dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId, name, email, hashedPassword, role, phone, college_id, major_id,
        dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone
      ]
    );

    successResponse(res, { userId }, '用户创建成功', 201);

  } catch (error) {
    console.error('创建用户错误:', error);
    errorResponse(res, '创建用户失败', 500, error);
  }
};

// 更新用户
export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      email,
      role,
      phone,
      college,
      major,
      dorm_building_id,
      room_number,
      emergency_contact_name,
      emergency_contact_phone
    } = req.body;

    // 检查用户是否存在
    const [existingUsers] = await pool.execute('SELECT id FROM users WHERE id = ?', [id]);
    if (existingUsers.length === 0) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 检查邮箱是否被其他用户使用
    if (email) {
      const [emailUsers] = await pool.execute('SELECT id FROM users WHERE email = ? AND id != ?', [email, id]);
      if (emailUsers.length > 0) {
        return errorResponse(res, '该邮箱已被其他用户使用', 409);
      }
    }

    // 获取学院和专业ID
    let college_id = null, major_id = null;
    
    if (college) {
      const [collegeRows] = await pool.execute('SELECT id FROM colleges WHERE name = ?', [college]);
      if (collegeRows.length > 0) {
        college_id = collegeRows[0].id;
      }
    }
    
    if (major) {
      const [majorRows] = await pool.execute('SELECT id FROM majors WHERE name = ?', [major]);
      if (majorRows.length > 0) {
        major_id = majorRows[0].id;
      }
    }

    // 更新用户
    await pool.execute(
      `UPDATE users SET 
       name = COALESCE(?, name),
       email = COALESCE(?, email),
       role = COALESCE(?, role),
       phone = ?,
       college_id = ?,
       major_id = ?,
       dorm_building_id = ?,
       room_number = ?,
       emergency_contact_name = ?,
       emergency_contact_phone = ?,
       updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [
        name, email, role, phone, college_id, major_id,
        dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone, id
      ]
    );

    successResponse(res, null, '用户更新成功');

  } catch (error) {
    console.error('更新用户错误:', error);
    errorResponse(res, '更新用户失败', 500, error);
  }
};

// 删除用户
export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const [existingUsers] = await pool.execute('SELECT id FROM users WHERE id = ?', [id]);
    if (existingUsers.length === 0) {
      return errorResponse(res, '用户不存在', 404);
    }

    // 删除用户
    await pool.execute('DELETE FROM users WHERE id = ?', [id]);

    successResponse(res, null, '用户删除成功');

  } catch (error) {
    console.error('删除用户错误:', error);
    errorResponse(res, '删除用户失败', 500, error);
  }
};

// 获取宿舍管理员列表
export const getDormAdmins = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT u.id, u.name, u.email, db.name as dormBuilding
       FROM users u 
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id 
       WHERE u.role = '宿舍管理员'
       ORDER BY u.name`
    );

    successResponse(res, { dormAdmins: rows });
  } catch (error) {
    console.error('获取宿舍管理员列表错误:', error);
    errorResponse(res, '获取宿舍管理员列表失败', 500, error);
  }
};

// 获取维修人员列表
export const getRepairStaff = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT id, name, email, phone
       FROM users 
       WHERE role = '维修人员'
       ORDER BY name`
    );

    successResponse(res, { repairStaff: rows });
  } catch (error) {
    console.error('获取维修人员列表错误:', error);
    errorResponse(res, '获取维修人员列表失败', 500, error);
  }
};
