import express from 'express';
import {
  getAllRepairRequests,
  getRepairRequestById,
  createRepairRequest,
  updateRepairRequestStatus,
  deleteRepairRequest,
  assignRepairRequest
} from '../controllers/repairController.js';
import { authenticateToken, requireStudent, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取维修请求列表 - 根据用户角色过滤
router.get('/', getAllRepairRequests);

// 根据ID获取维修请求 - 根据用户角色过滤
router.get('/:id', getRepairRequestById);

// 创建维修请求 - 仅学生
router.post('/', requireStudent, createRepairRequest);

// 更新维修请求状态 - 根据用户角色
router.put('/:id/status', updateRepairRequestStatus);

// 指派维修请求 - 仅管理员
router.put('/:id/assign', requireAdmin, assignRepairRequest);

// 删除维修请求 - 系统管理员或请求的学生
router.delete('/:id', deleteRepairRequest);

export default router;
