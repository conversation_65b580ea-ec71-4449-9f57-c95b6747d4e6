import express from 'express';
import {
  getAllUsers,
  getUserById,
  createUser,
  updateUser,
  deleteUser,
  getDormAdmins,
  getRepairStaff
} from '../controllers/userController.js';
import { authenticateToken, requireSystemAdmin } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取所有用户 - 仅系统管理员
router.get('/', requireSystemAdmin, getAllUsers);

// 获取宿舍管理员列表 - 仅系统管理员
router.get('/dorm-admins', requireSystemAdmin, getDormAdmins);

// 获取维修人员列表 - 系统管理员和宿舍管理员
router.get('/repair-staff', getRepairStaff);

// 根据ID获取用户 - 仅系统管理员
router.get('/:id', requireSystemAdmin, getUserById);

// 创建用户 - 仅系统管理员
router.post('/', requireSystemAdmin, createUser);

// 更新用户 - 仅系统管理员
router.put('/:id', requireSystemAdmin, updateUser);

// 删除用户 - 仅系统管理员
router.delete('/:id', requireSystemAdmin, deleteUser);

export default router;
