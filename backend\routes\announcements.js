import express from 'express';
import {
  getAllAnnouncements,
  getAnnouncementById,
  createAnnouncement,
  updateAnnouncement,
  deleteAnnouncement
} from '../controllers/announcementController.js';
import { authenticateToken, requireAdmin } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取公告列表 - 根据用户角色过滤
router.get('/', getAllAnnouncements);

// 根据ID获取公告 - 所有用户都可以访问
router.get('/:id', getAnnouncementById);

// 创建公告 - 仅管理员
router.post('/', requireAdmin, createAnnouncement);

// 更新公告 - 公告作者或系统管理员
router.put('/:id', updateAnnouncement);

// 删除公告 - 公告作者或系统管理员
router.delete('/:id', deleteAnnouncement);

export default router;
