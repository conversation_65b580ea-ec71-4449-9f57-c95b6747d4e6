import React, { useState, FormEvent, useEffect } from 'react';
import { RepairRequest, RepairStatus, UserRole } from '../types';
import { repairService, userService } from '../api/services';
import { useAuth } from '../contexts/AuthContext';
import Table from '../components/Table';
import Button from '../components/Button';
import Modal from '../components/Modal';
import Input from '../components/Input';
import Card from '../components/Card';

const API_BASE_URL = 'http://localhost:3001/api';

const RepairPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [repairs, setRepairs] = useState<RepairRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [currentRepair, setCurrentRepair] = useState<RepairRequest | null>(null);
  const [newRepair, setNewRepair] = useState<Partial<RepairRequest>>({ status: RepairStatus.PENDING });
  const [assignmentModalOpen, setAssignmentModalOpen] = useState(false);
  const [assigningRepairId, setAssigningRepairId] = useState<string | null>(null);
  const [selectedRepairStaffId, setSelectedRepairStaffId] = useState<string>('');
  const [updateStatusModalOpen, setUpdateStatusModalOpen] = useState(false);
  const [updatingRepair, setUpdatingRepair] = useState<RepairRequest | null>(null);
  const [newStatus, setNewStatus] = useState<RepairStatus | ''>('');
  const [updateNotes, setUpdateNotes] = useState('');

  // 获取维修请求列表
  const fetchRepairs = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/repairs`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取维修请求失败');
      }

      const data = await response.json();
      if (data.success) {
        setRepairs(data.data.repairRequests || []);
      }
    } catch (error) {
      console.error('获取维修请求错误:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    if (currentUser) {
      fetchRepairs();
    }
  }, [currentUser]);

  if (!currentUser) return null;

  const [repairStaffList, setRepairStaffList] = useState<any[]>([]);

  // 加载维修人员列表
  useEffect(() => {
    const loadRepairStaff = async () => {
      try {
        const staff = await userService.getRepairStaff();
        setRepairStaffList(staff);
      } catch (error) {
        console.error('加载维修人员列表失败:', error);
      }
    };

    if (currentUser && (currentUser.role === UserRole.SYSTEM_ADMIN || currentUser.role === UserRole.DORM_ADMIN)) {
      loadRepairStaff();
    }
  }, [currentUser]);

  const filteredRepairs = repairs.filter(r => {
    if (currentUser.role === UserRole.STUDENT) return r.studentId === currentUser.id;
    if (currentUser.role === UserRole.DORM_ADMIN) return r.dormBuilding === currentUser.dormBuilding; 
    if (currentUser.role === UserRole.REPAIR_STAFF) return r.assignedTo === currentUser.id || r.status === RepairStatus.PENDING;
    return true; 
  });

  const columns = [
    { header: 'ID', accessor: 'id' as keyof RepairRequest, className: 'w-1/12 truncate' },
    { header: '问题描述', accessor: 'description' as keyof RepairRequest, render: (item: RepairRequest) => <span title={item.description}>{item.description.substring(0,30)}...</span> },
    { header: '房间号', accessor: 'roomNumber' as keyof RepairRequest },
    { header: '楼栋', accessor: 'dormBuilding' as keyof RepairRequest },
    { header: '状态', accessor: 'status' as keyof RepairRequest, render: (item: RepairRequest) => <span className={`px-2 py-1 text-xs font-semibold rounded-full ${ getStatusColor(item.status) }`}>{item.status}</span> },
    { header: '提交人', accessor: 'studentName' as keyof RepairRequest },
    { header: '指派给', accessor: (item: RepairRequest) => item.assignedToName || '未指派'},
    { 
      header: '操作', 
      accessor: 'id' as keyof RepairRequest, 
      render: (repair: RepairRequest) => (
        <div className="space-x-1 whitespace-nowrap">
          <Button size="sm" variant="ghost" onClick={() => handleViewDetails(repair)}><i className="fas fa-eye"></i></Button>
          {currentUser.role === UserRole.DORM_ADMIN && repair.status === RepairStatus.PENDING && (
            <Button size="sm" variant="secondary" onClick={() => handleOpenAssignmentModal(repair.id)}><i className="fas fa-user-plus"></i> 指派</Button>
          )}
          {(currentUser.role === UserRole.REPAIR_STAFF && (repair.status === RepairStatus.ASSIGNED || repair.status === RepairStatus.IN_PROGRESS)) && (
            <Button size="sm" variant="secondary" onClick={() => handleOpenUpdateStatusModal(repair)}><i className="fas fa-edit"></i> 更新</Button>
          )}
           {(currentUser.role === UserRole.STUDENT && repair.status === RepairStatus.COMPLETED) && (
            <Button size="sm" variant="primary" onClick={() => handleConfirmCompletion(repair.id)}>确认完成</Button>
          )}
        </div>
      )
    }
  ];
  
  const getStatusColor = (status: RepairStatus) => {
    switch(status) {
        case RepairStatus.PENDING: return 'bg-red-100 text-red-700';
        case RepairStatus.ASSIGNED: return 'bg-yellow-100 text-yellow-700';
        case RepairStatus.IN_PROGRESS: return 'bg-blue-100 text-blue-700';
        case RepairStatus.COMPLETED: return 'bg-green-100 text-green-700';
        case RepairStatus.CONFIRMED: return 'bg-purple-100 text-purple-700';
        default: return 'bg-gray-100 text-gray-700';
    }
  };

  const handleOpenModal = () => {
    setNewRepair({ 
      studentId: currentUser.id, 
      studentName: currentUser.name, 
      roomNumber: currentUser.roomNumber, 
      dormBuilding: currentUser.dormBuilding, 
      contact: currentUser.email, 
      status: RepairStatus.PENDING, 
      submittedAt: new Date().toISOString() 
    });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewRepair({ status: RepairStatus.PENDING });
  };
  
  const handleViewDetails = (repair: RepairRequest) => {
    setCurrentRepair(repair);
    setIsDetailModalOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setNewRepair(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if(newRepair.description && newRepair.contact) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/repairs`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            student_id: newRepair.studentId,
            student_name: newRepair.studentName,
            room_number: newRepair.roomNumber,
            dorm_building: newRepair.dormBuilding,
            description: newRepair.description,
            image_url: newRepair.imageUrl,
            contact: newRepair.contact
          }),
        });

        if (!response.ok) {
          throw new Error('创建维修请求失败');
        }

        const data = await response.json();
        if (data.success) {
          // 重新获取维修请求列表
          await fetchRepairs();
          handleCloseModal();
        }
      } catch (error) {
        console.error('创建维修请求错误:', error);
        alert('创建维修请求失败，请重试');
      }
    }
  };

  const handleOpenAssignmentModal = (repairId: string) => {
    setAssigningRepairId(repairId);
    setAssignmentModalOpen(true);
  };
  
  const handleAssignRepair = async () => {
    if (assigningRepairId && selectedRepairStaffId) {
      try {
        const staffMember = MOCK_USERS.find(u => u.id === selectedRepairStaffId);
        const token = localStorage.getItem('token');
        
        const response = await fetch(`${API_BASE_URL}/repairs/${assigningRepairId}/status`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: '已指派',
            assigned_to: selectedRepairStaffId,
            assigned_to_name: staffMember?.name,
            notes: `指派给 ${staffMember?.name}`
          }),
        });

        if (!response.ok) {
          throw new Error('指派维修任务失败');
        }

        const data = await response.json();
        if (data.success) {
          // 重新获取维修请求列表
          await fetchRepairs();
          setAssignmentModalOpen(false);
          setAssigningRepairId(null);
          setSelectedRepairStaffId('');
        }
      } catch (error) {
        console.error('指派维修任务错误:', error);
        alert('指派维修任务失败，请重试');
      }
    }
  };

  const handleOpenUpdateStatusModal = (repair: RepairRequest) => {
    setUpdatingRepair(repair);
    setNewStatus(repair.status);
    setUpdateNotes('');
    setUpdateStatusModalOpen(true);
  };

  const handleUpdateRepairStatus = async () => {
    if (updatingRepair && newStatus) {
      try {
        const token = localStorage.getItem('token');
        
        const response = await fetch(`${API_BASE_URL}/repairs/${updatingRepair.id}/status`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: newStatus,
            notes: updateNotes || `状态已更新为 ${newStatus}`
          }),
        });

        if (!response.ok) {
          throw new Error('更新维修状态失败');
        }

        const data = await response.json();
        if (data.success) {
          // 重新获取维修请求列表
          await fetchRepairs();
          setUpdateStatusModalOpen(false);
          setUpdatingRepair(null);
        }
      } catch (error) {
        console.error('更新维修状态错误:', error);
        alert('更新维修状态失败，请重试');
      }
    }
  };
  
  const handleConfirmCompletion = async (repairId: string) => {
    try {
      const token = localStorage.getItem('token');
      
      const response = await fetch(`${API_BASE_URL}/repairs/${repairId}/status`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: '已确认',
          notes: '学生已确认完成'
        }),
      });

      if (!response.ok) {
        throw new Error('确认完成失败');
      }

      const data = await response.json();
      if (data.success) {
        // 重新获取维修请求列表
        await fetchRepairs();
      }
    } catch (error) {
      console.error('确认完成错误:', error);
      alert('确认完成失败，请重试');
    }
  };

  if (isLoading) {
    return (
      <Card title="报修申请">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">加载中...</div>
        </div>
      </Card>
    );
  }

  return (
    <Card 
      title="报修申请" 
      actions={ currentUser.role === UserRole.STUDENT && <Button onClick={handleOpenModal} leftIcon={<i className="fas fa-plus mr-2"></i>}>提交报修</Button>}
    >
      <Table columns={columns} data={filteredRepairs} keyExtractor={(r) => r.id} />

      {/* Submit New Repair Modal (for Students) */}
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title="提交新报修申请">
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="description" label="问题描述" value={newRepair.description || ''} onChange={handleInputChange} required />
          { currentUser.role === UserRole.STUDENT &&
            <>
              <Input name="roomNumber" label="房间号" value={currentUser.roomNumber || ''} readOnly disabled />
              <Input name="dormBuilding" label="宿舍楼" value={currentUser.dormBuilding || ''} readOnly disabled />
            </>
          }
          <Input name="contact" label="联系方式 (邮箱/电话)" value={newRepair.contact || currentUser.email || ''} onChange={handleInputChange} required />
          <div>
            <label htmlFor="imageUrl" className="block text-sm font-medium text-gray-700 mb-1">图片链接 (可选)</label>
            <Input id="imageUrl" name="imageUrl" type="text" placeholder="https://example.com/image.jpg" value={newRepair.imageUrl || ''} onChange={handleInputChange} />
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">提交申请</Button>
          </div>
        </form>
      </Modal>
      
      {/* View Details Modal */}
      {currentRepair && (
        <Modal isOpen={isDetailModalOpen} onClose={() => setIsDetailModalOpen(false)} title={`报修详情 - ${currentRepair.id}`} size="lg">
            <div className="space-y-3">
                <p><strong>问题描述:</strong> {currentRepair.description}</p>
                <p><strong>房间:</strong> {currentRepair.roomNumber}, <strong>楼栋:</strong> {currentRepair.dormBuilding}</p>
                <p><strong>状态:</strong> <span className={`px-2 py-1 text-xs font-semibold rounded-full ${ getStatusColor(currentRepair.status) }`}>{currentRepair.status}</span></p>
                <p><strong>提交人:</strong> {currentRepair.studentName} ({currentRepair.contact})</p>
                <p><strong>提交时间:</strong> {new Date(currentRepair.submittedAt).toLocaleString()}</p>
                <p><strong>指派给:</strong> {currentRepair.assignedToName || '未指派'}</p>
                {currentRepair.imageUrl && <div><strong>图片:</strong> <img src={currentRepair.imageUrl} alt="报修图片" className="max-w-xs max-h-xs rounded mt-1"/></div>}
                {currentRepair.updates && currentRepair.updates.length > 0 && (
                    <div>
                        <h4 className="font-semibold mt-2">更新记录:</h4>
                        <ul className="list-disc list-inside text-sm space-y-1 max-h-40 overflow-y-auto">
                            {currentRepair.updates.map((update, index) => (
                                <li key={index}>
                                    {new Date(update.timestamp).toLocaleString()}: 由 {update.updatedBy} - {update.notes} {update.newStatus && `(状态改为 ${update.newStatus})`}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
             <div className="mt-6 flex justify-end">
                <Button onClick={() => setIsDetailModalOpen(false)}>关闭</Button>
            </div>
        </Modal>
      )}

      {/* Assign Repair Modal (for Dorm Admins) */}
      <Modal isOpen={assignmentModalOpen} onClose={() => setAssignmentModalOpen(false)} title="指派维修任务">
        <div className="space-y-4">
          <p>正在指派报修 ID: {assigningRepairId}</p>
          <div>
            <label htmlFor="repairStaff" className="block text-sm font-medium text-gray-700 mb-1">选择维修人员</label>
            <select
              id="repairStaff"
              value={selectedRepairStaffId}
              onChange={(e) => setSelectedRepairStaffId(e.target.value)}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">-- 选择维修人员 --</option>
              {repairStaffList.map(staff => (
                <option key={staff.id} value={staff.id}>{staff.name}</option>
              ))}
            </select>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="ghost" onClick={() => setAssignmentModalOpen(false)}>取消</Button>
            <Button onClick={handleAssignRepair} disabled={!selectedRepairStaffId}>指派</Button>
          </div>
        </div>
      </Modal>

      {/* Update Status Modal (for Repair Staff) */}
      {updatingRepair && (
        <Modal isOpen={updateStatusModalOpen} onClose={() => setUpdateStatusModalOpen(false)} title={`更新报修状态 - ${updatingRepair.id}`}>
            <div className="space-y-4">
                <p><strong>当前状态:</strong> {updatingRepair.status}</p>
                <div>
                    <label htmlFor="newStatus" className="block text-sm font-medium text-gray-700 mb-1">新状态</label>
                    <select 
                        id="newStatus" 
                        value={newStatus} 
                        onChange={(e) => setNewStatus(e.target.value as RepairStatus)}
                        className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    >
                        <option value="">-- 选择状态 --</option>
                        { currentUser.role === UserRole.REPAIR_STAFF && <>
                            <option value={RepairStatus.IN_PROGRESS}>{RepairStatus.IN_PROGRESS}</option>
                            <option value={RepairStatus.COMPLETED}>{RepairStatus.COMPLETED}</option>
                            </>
                        }
                    </select>
                </div>
                <div>
                    <label htmlFor="updateNotes" className="block text-sm font-medium text-gray-700 mb-1">备注 (可选)</label>
                    <textarea 
                        id="updateNotes" 
                        value={updateNotes} 
                        onChange={(e) => setUpdateNotes(e.target.value)}
                        rows={3}
                        className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    />
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                    <Button variant="ghost" onClick={() => setUpdateStatusModalOpen(false)}>取消</Button>
                    <Button onClick={handleUpdateRepairStatus} disabled={!newStatus}>更新状态</Button>
                </div>
            </div>
        </Modal>
      )}

    </Card>
  );
};

export default RepairPage;