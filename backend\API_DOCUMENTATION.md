# 易宿管 - API 文档

## 基础信息

- **基础URL**: `http://localhost:3001/api`
- **认证方式**: <PERSON><PERSON> (JWT)
- **内容类型**: `application/json`

## 认证相关 API

### 用户登录
- **POST** `/auth/login`
- **请求体**:
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```
- **响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": "user_id",
      "name": "用户名",
      "email": "<EMAIL>",
      "role": "学生",
      "phone": "13000000001",
      "college": "工程学院",
      "major": "计算机科学",
      "dormBuilding": "A栋 (阿尔法楼)",
      "roomNumber": "101"
    },
    "token": "jwt_token_here"
  }
}
```

### 用户注册
- **POST** `/auth/register`
- **请求体**:
```json
{
  "name": "新用户",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "学生"
}
```

### 获取当前用户信息
- **GET** `/auth/me`
- **需要认证**: 是

### 修改密码
- **POST** `/auth/change-password`
- **需要认证**: 是
- **请求体**:
```json
{
  "currentPassword": "old_password",
  "newPassword": "new_password"
}
```

## 用户管理 API

### 获取所有用户
- **GET** `/users`
- **需要认证**: 是 (仅系统管理员)

### 获取用户详情
- **GET** `/users/{id}`
- **需要认证**: 是 (仅系统管理员)

### 创建用户
- **POST** `/users`
- **需要认证**: 是 (仅系统管理员)
- **请求体**:
```json
{
  "name": "用户名",
  "email": "<EMAIL>",
  "password": "password123",
  "role": "学生",
  "phone": "13000000001",
  "college": "工程学院",
  "major": "计算机科学",
  "dorm_building_id": "bldgA",
  "room_number": "101"
}
```

### 更新用户
- **PUT** `/users/{id}`
- **需要认证**: 是 (仅系统管理员)

### 删除用户
- **DELETE** `/users/{id}`
- **需要认证**: 是 (仅系统管理员)

### 获取宿舍管理员列表
- **GET** `/users/dorm-admins`
- **需要认证**: 是 (仅系统管理员)

### 获取维修人员列表
- **GET** `/users/repair-staff`
- **需要认证**: 是

## 学院管理 API

### 获取所有学院
- **GET** `/colleges`
- **需要认证**: 是

### 创建学院
- **POST** `/colleges`
- **需要认证**: 是 (仅系统管理员)
- **请求体**:
```json
{
  "name": "学院名称"
}
```

### 更新学院
- **PUT** `/colleges/{id}`
- **需要认证**: 是 (仅系统管理员)

### 删除学院
- **DELETE** `/colleges/{id}`
- **需要认证**: 是 (仅系统管理员)

## 专业管理 API

### 获取所有专业
- **GET** `/majors`
- **需要认证**: 是

### 根据学院获取专业
- **GET** `/majors/college/{college_id}`
- **需要认证**: 是

### 创建专业
- **POST** `/majors`
- **需要认证**: 是 (仅系统管理员)
- **请求体**:
```json
{
  "name": "专业名称",
  "college_id": "学院ID"
}
```

## 宿舍楼管理 API

### 获取所有宿舍楼
- **GET** `/dorm-buildings`
- **需要认证**: 是

### 创建宿舍楼
- **POST** `/dorm-buildings`
- **需要认证**: 是 (仅系统管理员)
- **请求体**:
```json
{
  "name": "宿舍楼名称",
  "floors": 5,
  "totalRooms": 50,
  "assignedAdminId": "管理员ID"
}
```

## 房间管理 API

### 获取所有房间
- **GET** `/rooms`
- **需要认证**: 是

### 获取房间床位
- **GET** `/rooms/{id}/beds`
- **需要认证**: 是

### 创建房间
- **POST** `/rooms`
- **需要认证**: 是 (仅管理员)
- **请求体**:
```json
{
  "roomNumber": "101",
  "dormBuildingId": "宿舍楼ID",
  "floor": 1,
  "type": "四人间",
  "capacity": 4
}
```

### 分配床位
- **PUT** `/rooms/beds/{bedId}/assign`
- **需要认证**: 是 (仅管理员)
- **请求体**:
```json
{
  "studentId": "学生ID"
}
```

### 释放床位
- **PUT** `/rooms/beds/{bedId}/release`
- **需要认证**: 是 (仅管理员)

## 维修请求 API

### 获取维修请求列表
- **GET** `/repairs`
- **需要认证**: 是
- **查询参数**:
  - `status`: 状态过滤
  - `studentId`: 学生ID过滤
  - `assignedTo`: 指派人过滤

### 创建维修请求
- **POST** `/repairs`
- **需要认证**: 是 (仅学生)
- **请求体**:
```json
{
  "description": "维修描述",
  "imageUrl": "图片URL (可选)",
  "contact": "联系方式"
}
```

### 更新维修请求状态
- **PUT** `/repairs/{id}/status`
- **需要认证**: 是
- **请求体**:
```json
{
  "status": "新状态",
  "notes": "备注"
}
```

### 指派维修请求
- **PUT** `/repairs/{id}/assign`
- **需要认证**: 是 (仅管理员)
- **请求体**:
```json
{
  "assignedTo": "维修人员ID"
}
```

## 公告管理 API

### 获取公告列表
- **GET** `/announcements`
- **需要认证**: 是
- **查询参数**:
  - `scope`: 范围过滤 (All/College/DormBuilding)
  - `limit`: 数量限制

### 创建公告
- **POST** `/announcements`
- **需要认证**: 是 (仅管理员)
- **请求体**:
```json
{
  "title": "公告标题",
  "content": "公告内容",
  "scope": "All",
  "targetId": "目标ID (可选)"
}
```

## 水电费管理 API

### 获取水电费列表
- **GET** `/utility-bills`
- **需要认证**: 是
- **查询参数**:
  - `month`: 月份过滤 (YYYY-MM)
  - `isPaid`: 支付状态过滤
  - `studentId`: 学生ID过滤

### 创建水电费记录
- **POST** `/utility-bills`
- **需要认证**: 是 (仅管理员)
- **请求体**:
```json
{
  "studentId": "学生ID",
  "roomId": "房间ID",
  "month": "2024-07",
  "electricityUsage": 120.5,
  "waterUsage": 15.3
}
```

### 标记为已支付
- **PUT** `/utility-bills/{id}/pay`
- **需要认证**: 是

### 获取学生水电费统计
- **GET** `/utility-bills/stats/{studentId}`
- **需要认证**: 是

## 错误响应格式

```json
{
  "success": false,
  "message": "错误信息",
  "timestamp": "2024-07-20T10:00:00.000Z"
}
```

## 状态码

- `200`: 成功
- `201`: 创建成功
- `400`: 请求错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `409`: 冲突 (如重复数据)
- `422`: 数据验证失败
- `500`: 服务器错误
