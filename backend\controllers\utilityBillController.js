import { pool } from '../config/database.js';
import { successResponse, errorResponse } from '../utils/response.js';

// 获取水电费列表
export const getAllUtilityBills = async (req, res) => {
  try {
    const currentUser = req.user;
    const { month, isPaid, studentId } = req.query;

    let query = `
      SELECT ub.*, u.name as student_name, r.room_number, db.name as dorm_building_name
      FROM utility_bills ub
      LEFT JOIN users u ON ub.student_id = u.id
      LEFT JOIN rooms r ON ub.room_id = r.id
      LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
      WHERE 1=1
    `;
    const params = [];

    // 根据用户角色过滤数据
    if (currentUser.role === '学生') {
      query += ' AND ub.student_id = ?';
      params.push(currentUser.id);
    } else if (currentUser.role === '宿舍管理员') {
      query += ' AND db.assigned_admin_id = ?';
      params.push(currentUser.id);
    }

    // 添加其他过滤条件
    if (month) {
      query += ' AND ub.month = ?';
      params.push(month);
    }

    if (isPaid !== undefined) {
      query += ' AND ub.is_paid = ?';
      params.push(isPaid === 'true');
    }

    if (studentId) {
      query += ' AND ub.student_id = ?';
      params.push(studentId);
    }

    query += ' ORDER BY ub.month DESC, u.name';

    const [rows] = await pool.execute(query, params);
    successResponse(res, { utilityBills: rows });
  } catch (error) {
    console.error('获取水电费列表错误:', error);
    errorResponse(res, '获取水电费列表失败', 500, error);
  }
};

// 根据ID获取水电费
export const getUtilityBillById = async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    const [rows] = await pool.execute(
      `SELECT ub.*, u.name as student_name, r.room_number, db.name as dorm_building_name
       FROM utility_bills ub
       LEFT JOIN users u ON ub.student_id = u.id
       LEFT JOIN rooms r ON ub.room_id = r.id
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       WHERE ub.id = ?`,
      [id]
    );

    if (rows.length === 0) {
      return errorResponse(res, '水电费记录不存在', 404);
    }

    const bill = rows[0];

    // 权限检查
    if (currentUser.role === '学生' && bill.student_id !== currentUser.id) {
      return errorResponse(res, '无权限查看此水电费记录', 403);
    }

    if (currentUser.role === '宿舍管理员' && bill.dorm_building_name !== currentUser.dorm_building_name) {
      return errorResponse(res, '无权限查看此水电费记录', 403);
    }

    successResponse(res, { utilityBill: bill });
  } catch (error) {
    console.error('获取水电费详情错误:', error);
    errorResponse(res, '获取水电费详情失败', 500, error);
  }
};

// 创建水电费记录
export const createUtilityBill = async (req, res) => {
  try {
    const {
      studentId,
      roomId,
      month,
      electricityUsage,
      electricityCost,
      waterUsage,
      waterCost,
      totalCost
    } = req.body;

    const currentUser = req.user;

    // 只有管理员可以创建水电费记录
    if (!['系统管理员', '宿舍管理员'].includes(currentUser.role)) {
      return errorResponse(res, '无权限创建水电费记录', 403);
    }

    if (!studentId || !roomId || !month || electricityUsage === undefined || waterUsage === undefined) {
      return errorResponse(res, '学生ID、房间ID、月份、用电量和用水量不能为空', 400);
    }

    // 检查学生是否存在
    const [students] = await pool.execute('SELECT id FROM users WHERE id = ? AND role = ?', [studentId, '学生']);
    if (students.length === 0) {
      return errorResponse(res, '指定的学生不存在', 404);
    }

    // 检查房间是否存在
    const [rooms] = await pool.execute(
      `SELECT r.*, db.assigned_admin_id
       FROM rooms r
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       WHERE r.id = ?`,
      [roomId]
    );

    if (rooms.length === 0) {
      return errorResponse(res, '指定的房间不存在', 404);
    }

    const room = rooms[0];

    // 宿舍管理员只能为自己管理的宿舍楼创建水电费记录
    if (currentUser.role === '宿舍管理员' && room.assigned_admin_id !== currentUser.id) {
      return errorResponse(res, '只能为自己管理的宿舍楼创建水电费记录', 403);
    }

    // 检查该学生该月份是否已有水电费记录
    const [existingBills] = await pool.execute(
      'SELECT id FROM utility_bills WHERE student_id = ? AND month = ?',
      [studentId, month]
    );

    if (existingBills.length > 0) {
      return errorResponse(res, '该学生该月份已有水电费记录', 409);
    }

    // 计算费用（如果没有提供）
    const finalElectricityCost = electricityCost || (electricityUsage * 0.5); // 假设每度电0.5元
    const finalWaterCost = waterCost || (waterUsage * 3.0); // 假设每立方米水3元
    const finalTotalCost = totalCost || (finalElectricityCost + finalWaterCost);

    // 生成水电费ID
    const billId = 'bill_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 插入水电费记录
    await pool.execute(
      `INSERT INTO utility_bills (id, student_id, room_id, month, electricity_usage, electricity_cost, 
       water_usage, water_cost, total_cost, is_paid) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, FALSE)`,
      [
        billId, studentId, roomId, month, electricityUsage, finalElectricityCost,
        waterUsage, finalWaterCost, finalTotalCost
      ]
    );

    successResponse(res, { billId }, '水电费记录创建成功', 201);

  } catch (error) {
    console.error('创建水电费记录错误:', error);
    errorResponse(res, '创建水电费记录失败', 500, error);
  }
};

// 更新水电费记录
export const updateUtilityBill = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      electricityUsage,
      electricityCost,
      waterUsage,
      waterCost,
      totalCost,
      isPaid
    } = req.body;

    const currentUser = req.user;

    // 只有管理员可以更新水电费记录
    if (!['系统管理员', '宿舍管理员'].includes(currentUser.role)) {
      return errorResponse(res, '无权限更新水电费记录', 403);
    }

    // 检查水电费记录是否存在
    const [existingBills] = await pool.execute(
      `SELECT ub.*, r.dorm_building_id, db.assigned_admin_id
       FROM utility_bills ub
       LEFT JOIN rooms r ON ub.room_id = r.id
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       WHERE ub.id = ?`,
      [id]
    );

    if (existingBills.length === 0) {
      return errorResponse(res, '水电费记录不存在', 404);
    }

    const bill = existingBills[0];

    // 宿舍管理员只能更新自己管理的宿舍楼的水电费记录
    if (currentUser.role === '宿舍管理员' && bill.assigned_admin_id !== currentUser.id) {
      return errorResponse(res, '只能更新自己管理的宿舍楼的水电费记录', 403);
    }

    // 更新水电费记录
    const updateFields = [];
    const updateParams = [];

    if (electricityUsage !== undefined) {
      updateFields.push('electricity_usage = ?');
      updateParams.push(electricityUsage);
    }

    if (electricityCost !== undefined) {
      updateFields.push('electricity_cost = ?');
      updateParams.push(electricityCost);
    }

    if (waterUsage !== undefined) {
      updateFields.push('water_usage = ?');
      updateParams.push(waterUsage);
    }

    if (waterCost !== undefined) {
      updateFields.push('water_cost = ?');
      updateParams.push(waterCost);
    }

    if (totalCost !== undefined) {
      updateFields.push('total_cost = ?');
      updateParams.push(totalCost);
    }

    if (isPaid !== undefined) {
      updateFields.push('is_paid = ?');
      updateParams.push(isPaid);
    }

    if (updateFields.length > 0) {
      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      updateParams.push(id);

      await pool.execute(
        `UPDATE utility_bills SET ${updateFields.join(', ')} WHERE id = ?`,
        updateParams
      );
    }

    successResponse(res, null, '水电费记录更新成功');

  } catch (error) {
    console.error('更新水电费记录错误:', error);
    errorResponse(res, '更新水电费记录失败', 500, error);
  }
};

// 删除水电费记录
export const deleteUtilityBill = async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // 只有系统管理员可以删除水电费记录
    if (currentUser.role !== '系统管理员') {
      return errorResponse(res, '无权限删除水电费记录', 403);
    }

    // 检查水电费记录是否存在
    const [existingBills] = await pool.execute('SELECT id FROM utility_bills WHERE id = ?', [id]);
    if (existingBills.length === 0) {
      return errorResponse(res, '水电费记录不存在', 404);
    }

    // 删除水电费记录
    await pool.execute('DELETE FROM utility_bills WHERE id = ?', [id]);

    successResponse(res, null, '水电费记录删除成功');

  } catch (error) {
    console.error('删除水电费记录错误:', error);
    errorResponse(res, '删除水电费记录失败', 500, error);
  }
};

// 标记水电费为已支付
export const markAsPaid = async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // 检查水电费记录是否存在
    const [existingBills] = await pool.execute(
      `SELECT ub.*, r.dorm_building_id, db.assigned_admin_id
       FROM utility_bills ub
       LEFT JOIN rooms r ON ub.room_id = r.id
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       WHERE ub.id = ?`,
      [id]
    );

    if (existingBills.length === 0) {
      return errorResponse(res, '水电费记录不存在', 404);
    }

    const bill = existingBills[0];

    // 权限检查：学生只能标记自己的账单，管理员可以标记管理范围内的账单
    if (currentUser.role === '学生' && bill.student_id !== currentUser.id) {
      return errorResponse(res, '无权限操作此水电费记录', 403);
    }

    if (currentUser.role === '宿舍管理员' && bill.assigned_admin_id !== currentUser.id) {
      return errorResponse(res, '无权限操作此水电费记录', 403);
    }

    // 标记为已支付
    await pool.execute(
      'UPDATE utility_bills SET is_paid = TRUE, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );

    successResponse(res, null, '水电费标记为已支付');

  } catch (error) {
    console.error('标记水电费支付错误:', error);
    errorResponse(res, '标记水电费支付失败', 500, error);
  }
};

// 获取学生的水电费统计
export const getUtilityBillStats = async (req, res) => {
  try {
    const { studentId } = req.params;
    const currentUser = req.user;

    // 权限检查
    if (currentUser.role === '学生' && studentId !== currentUser.id) {
      return errorResponse(res, '无权限查看此学生的水电费统计', 403);
    }

    const [rows] = await pool.execute(
      `SELECT 
         COUNT(*) as total_bills,
         SUM(CASE WHEN is_paid = TRUE THEN 1 ELSE 0 END) as paid_bills,
         SUM(CASE WHEN is_paid = FALSE THEN 1 ELSE 0 END) as unpaid_bills,
         SUM(total_cost) as total_amount,
         SUM(CASE WHEN is_paid = TRUE THEN total_cost ELSE 0 END) as paid_amount,
         SUM(CASE WHEN is_paid = FALSE THEN total_cost ELSE 0 END) as unpaid_amount,
         AVG(electricity_usage) as avg_electricity_usage,
         AVG(water_usage) as avg_water_usage
       FROM utility_bills 
       WHERE student_id = ?`,
      [studentId]
    );

    successResponse(res, { stats: rows[0] });
  } catch (error) {
    console.error('获取水电费统计错误:', error);
    errorResponse(res, '获取水电费统计失败', 500, error);
  }
};
