import { pool } from '../config/database.js';
import { successResponse, errorResponse } from '../utils/response.js';

// 获取所有房间
export const getAllRooms = async (req, res) => {
  try {
    const currentUser = req.user;
    let query = `
      SELECT r.*, db.name as dorm_building_name
      FROM rooms r
      LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
    `;
    const params = [];

    // 根据用户角色过滤数据
    if (currentUser.role === '宿舍管理员') {
      query += ' WHERE r.dorm_building_id = ?';
      params.push(currentUser.dorm_building_id);
    }

    query += ' ORDER BY db.name, r.floor, r.room_number';

    const [rows] = await pool.execute(query, params);
    successResponse(res, { rooms: rows });
  } catch (error) {
    console.error('获取房间列表错误:', error);
    errorResponse(res, '获取房间列表失败', 500, error);
  }
};

// 根据ID获取房间
export const getRoomById = async (req, res) => {
  try {
    const { id } = req.params;

    const [rows] = await pool.execute(
      `SELECT r.*, db.name as dorm_building_name
       FROM rooms r
       LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
       WHERE r.id = ?`,
      [id]
    );

    if (rows.length === 0) {
      return errorResponse(res, '房间不存在', 404);
    }

    successResponse(res, { room: rows[0] });
  } catch (error) {
    console.error('获取房间详情错误:', error);
    errorResponse(res, '获取房间详情失败', 500, error);
  }
};

// 创建房间
export const createRoom = async (req, res) => {
  try {
    const { roomNumber, dormBuildingId, floor, type, capacity } = req.body;
    const currentUser = req.user;

    // 只有管理员可以创建房间
    if (!['系统管理员', '宿舍管理员'].includes(currentUser.role)) {
      return errorResponse(res, '无权限创建房间', 403);
    }

    if (!roomNumber || !dormBuildingId || !floor || !type || !capacity) {
      return errorResponse(res, '房间号、宿舍楼、楼层、类型和容量不能为空', 400);
    }

    // 宿舍管理员只能在自己管理的宿舍楼创建房间
    if (currentUser.role === '宿舍管理员' && dormBuildingId !== currentUser.dorm_building_id) {
      return errorResponse(res, '只能在自己管理的宿舍楼创建房间', 403);
    }

    // 检查房间号是否已存在
    const [existingRooms] = await pool.execute(
      'SELECT id FROM rooms WHERE room_number = ? AND dorm_building_id = ?',
      [roomNumber, dormBuildingId]
    );

    if (existingRooms.length > 0) {
      return errorResponse(res, '该宿舍楼已存在相同房间号', 409);
    }

    // 检查宿舍楼是否存在
    const [buildings] = await pool.execute('SELECT id FROM dorm_buildings WHERE id = ?', [dormBuildingId]);
    if (buildings.length === 0) {
      return errorResponse(res, '指定的宿舍楼不存在', 404);
    }

    // 生成房间ID
    const roomId = 'room_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 插入房间
    await pool.execute(
      'INSERT INTO rooms (id, room_number, dorm_building_id, floor, type, capacity, occupied_beds) VALUES (?, ?, ?, ?, ?, ?, 0)',
      [roomId, roomNumber, dormBuildingId, floor, type, capacity]
    );

    // 创建床位
    const bedInserts = [];
    for (let i = 1; i <= capacity; i++) {
      const bedId = `bed_${roomId}_${i}`;
      bedInserts.push([bedId, roomId, i.toString(), '空闲', null]);
    }

    if (bedInserts.length > 0) {
      await pool.execute(
        `INSERT INTO beds (id, room_id, bed_number, status, student_id) VALUES ${bedInserts.map(() => '(?, ?, ?, ?, ?)').join(', ')}`,
        bedInserts.flat()
      );
    }

    successResponse(res, { roomId }, '房间创建成功', 201);

  } catch (error) {
    console.error('创建房间错误:', error);
    errorResponse(res, '创建房间失败', 500, error);
  }
};

// 更新房间
export const updateRoom = async (req, res) => {
  try {
    const { id } = req.params;
    const { roomNumber, floor, type, capacity } = req.body;
    const currentUser = req.user;

    // 只有管理员可以更新房间
    if (!['系统管理员', '宿舍管理员'].includes(currentUser.role)) {
      return errorResponse(res, '无权限更新房间', 403);
    }

    // 检查房间是否存在
    const [existingRooms] = await pool.execute('SELECT * FROM rooms WHERE id = ?', [id]);
    if (existingRooms.length === 0) {
      return errorResponse(res, '房间不存在', 404);
    }

    const room = existingRooms[0];

    // 宿舍管理员只能更新自己管理的宿舍楼的房间
    if (currentUser.role === '宿舍管理员' && room.dorm_building_id !== currentUser.dorm_building_id) {
      return errorResponse(res, '只能更新自己管理的宿舍楼的房间', 403);
    }

    // 检查房间号是否被其他房间使用
    if (roomNumber) {
      const [nameConflict] = await pool.execute(
        'SELECT id FROM rooms WHERE room_number = ? AND dorm_building_id = ? AND id != ?',
        [roomNumber, room.dorm_building_id, id]
      );

      if (nameConflict.length > 0) {
        return errorResponse(res, '该宿舍楼已存在相同房间号', 409);
      }
    }

    // 更新房间
    await pool.execute(
      `UPDATE rooms SET 
       room_number = COALESCE(?, room_number),
       floor = COALESCE(?, floor),
       type = COALESCE(?, type),
       capacity = COALESCE(?, capacity),
       updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [roomNumber, floor, type, capacity, id]
    );

    successResponse(res, null, '房间更新成功');

  } catch (error) {
    console.error('更新房间错误:', error);
    errorResponse(res, '更新房间失败', 500, error);
  }
};

// 删除房间
export const deleteRoom = async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // 只有管理员可以删除房间
    if (!['系统管理员', '宿舍管理员'].includes(currentUser.role)) {
      return errorResponse(res, '无权限删除房间', 403);
    }

    // 检查房间是否存在
    const [existingRooms] = await pool.execute('SELECT * FROM rooms WHERE id = ?', [id]);
    if (existingRooms.length === 0) {
      return errorResponse(res, '房间不存在', 404);
    }

    const room = existingRooms[0];

    // 宿舍管理员只能删除自己管理的宿舍楼的房间
    if (currentUser.role === '宿舍管理员' && room.dorm_building_id !== currentUser.dorm_building_id) {
      return errorResponse(res, '只能删除自己管理的宿舍楼的房间', 403);
    }

    // 检查房间是否有学生入住
    if (room.occupied_beds > 0) {
      return errorResponse(res, '房间内还有学生入住，无法删除', 400);
    }

    // 删除房间（会级联删除床位）
    await pool.execute('DELETE FROM rooms WHERE id = ?', [id]);

    successResponse(res, null, '房间删除成功');

  } catch (error) {
    console.error('删除房间错误:', error);
    errorResponse(res, '删除房间失败', 500, error);
  }
};

// 获取房间的床位列表
export const getRoomBeds = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查房间是否存在
    const [rooms] = await pool.execute('SELECT id FROM rooms WHERE id = ?', [id]);
    if (rooms.length === 0) {
      return errorResponse(res, '房间不存在', 404);
    }

    const [rows] = await pool.execute(
      `SELECT b.*, u.name as student_name
       FROM beds b
       LEFT JOIN users u ON b.student_id = u.id
       WHERE b.room_id = ?
       ORDER BY CAST(b.bed_number AS UNSIGNED)`,
      [id]
    );

    successResponse(res, { beds: rows });
  } catch (error) {
    console.error('获取房间床位列表错误:', error);
    errorResponse(res, '获取房间床位列表失败', 500, error);
  }
};

// 分配床位给学生
export const assignBed = async (req, res) => {
  try {
    const { bedId } = req.params;
    const { studentId } = req.body;
    const currentUser = req.user;

    // 只有管理员可以分配床位
    if (!['系统管理员', '宿舍管理员'].includes(currentUser.role)) {
      return errorResponse(res, '无权限分配床位', 403);
    }

    if (!studentId) {
      return errorResponse(res, '学生ID不能为空', 400);
    }

    // 检查床位是否存在
    const [beds] = await pool.execute(
      `SELECT b.*, r.dorm_building_id
       FROM beds b
       LEFT JOIN rooms r ON b.room_id = r.id
       WHERE b.id = ?`,
      [bedId]
    );

    if (beds.length === 0) {
      return errorResponse(res, '床位不存在', 404);
    }

    const bed = beds[0];

    // 宿舍管理员只能分配自己管理的宿舍楼的床位
    if (currentUser.role === '宿舍管理员' && bed.dorm_building_id !== currentUser.dorm_building_id) {
      return errorResponse(res, '只能分配自己管理的宿舍楼的床位', 403);
    }

    // 检查床位是否已被占用
    if (bed.status === '已入住') {
      return errorResponse(res, '床位已被占用', 400);
    }

    // 检查学生是否存在且为学生角色
    const [students] = await pool.execute(
      'SELECT id FROM users WHERE id = ? AND role = ?',
      [studentId, '学生']
    );

    if (students.length === 0) {
      return errorResponse(res, '指定的学生不存在', 404);
    }

    // 检查学生是否已有床位
    const [existingBeds] = await pool.execute(
      'SELECT id FROM beds WHERE student_id = ?',
      [studentId]
    );

    if (existingBeds.length > 0) {
      return errorResponse(res, '该学生已有床位', 400);
    }

    // 分配床位
    await pool.execute(
      'UPDATE beds SET status = ?, student_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['已入住', studentId, bedId]
    );

    // 更新房间的已住人数
    await pool.execute(
      'UPDATE rooms SET occupied_beds = occupied_beds + 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [bed.room_id]
    );

    successResponse(res, null, '床位分配成功');

  } catch (error) {
    console.error('分配床位错误:', error);
    errorResponse(res, '分配床位失败', 500, error);
  }
};

// 释放床位
export const releaseBed = async (req, res) => {
  try {
    const { bedId } = req.params;
    const currentUser = req.user;

    // 只有管理员可以释放床位
    if (!['系统管理员', '宿舍管理员'].includes(currentUser.role)) {
      return errorResponse(res, '无权限释放床位', 403);
    }

    // 检查床位是否存在
    const [beds] = await pool.execute(
      `SELECT b.*, r.dorm_building_id
       FROM beds b
       LEFT JOIN rooms r ON b.room_id = r.id
       WHERE b.id = ?`,
      [bedId]
    );

    if (beds.length === 0) {
      return errorResponse(res, '床位不存在', 404);
    }

    const bed = beds[0];

    // 宿舍管理员只能释放自己管理的宿舍楼的床位
    if (currentUser.role === '宿舍管理员' && bed.dorm_building_id !== currentUser.dorm_building_id) {
      return errorResponse(res, '只能释放自己管理的宿舍楼的床位', 403);
    }

    // 检查床位是否已被占用
    if (bed.status === '空闲') {
      return errorResponse(res, '床位未被占用', 400);
    }

    // 释放床位
    await pool.execute(
      'UPDATE beds SET status = ?, student_id = NULL, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      ['空闲', bedId]
    );

    // 更新房间的已住人数
    await pool.execute(
      'UPDATE rooms SET occupied_beds = occupied_beds - 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [bed.room_id]
    );

    successResponse(res, null, '床位释放成功');

  } catch (error) {
    console.error('释放床位错误:', error);
    errorResponse(res, '释放床位失败', 500, error);
  }
};
