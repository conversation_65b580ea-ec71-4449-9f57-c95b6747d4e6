{"name": "yisuguan-backend", "version": "1.0.0", "description": "易宿管 - 智能宿舍管理系统后端API", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dormitory", "management", "system", "api", "nodejs", "mysql"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}