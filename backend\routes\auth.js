import express from 'express';
import { login, register, getCurrentUser, changePassword } from '../controllers/authController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 公开路由
router.post('/login', login);
router.post('/register', register);

// 需要认证的路由
router.get('/me', authenticateToken, getCurrentUser);
router.post('/change-password', authenticateToken, changePassword);

export default router;
