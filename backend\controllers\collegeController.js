import { pool } from '../config/database.js';
import { successResponse, errorResponse } from '../utils/response.js';

// 获取所有学院
export const getAllColleges = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      'SELECT id, name, created_at FROM colleges ORDER BY name'
    );

    successResponse(res, { colleges: rows });
  } catch (error) {
    console.error('获取学院列表错误:', error);
    errorResponse(res, '获取学院列表失败', 500, error);
  }
};

// 根据ID获取学院
export const getCollegeById = async (req, res) => {
  try {
    const { id } = req.params;

    const [rows] = await pool.execute(
      'SELECT * FROM colleges WHERE id = ?',
      [id]
    );

    if (rows.length === 0) {
      return errorResponse(res, '学院不存在', 404);
    }

    successResponse(res, { college: rows[0] });
  } catch (error) {
    console.error('获取学院详情错误:', error);
    errorResponse(res, '获取学院详情失败', 500, error);
  }
};

// 创建学院
export const createCollege = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return errorResponse(res, '学院名称不能为空', 400);
    }

    // 检查学院名称是否已存在
    const [existingColleges] = await pool.execute(
      'SELECT id FROM colleges WHERE name = ?',
      [name]
    );

    if (existingColleges.length > 0) {
      return errorResponse(res, '该学院名称已存在', 409);
    }

    // 生成学院ID
    const collegeId = 'col_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 插入学院
    await pool.execute(
      'INSERT INTO colleges (id, name) VALUES (?, ?)',
      [collegeId, name]
    );

    successResponse(res, { collegeId }, '学院创建成功', 201);

  } catch (error) {
    console.error('创建学院错误:', error);
    errorResponse(res, '创建学院失败', 500, error);
  }
};

// 更新学院
export const updateCollege = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return errorResponse(res, '学院名称不能为空', 400);
    }

    // 检查学院是否存在
    const [existingColleges] = await pool.execute('SELECT id FROM colleges WHERE id = ?', [id]);
    if (existingColleges.length === 0) {
      return errorResponse(res, '学院不存在', 404);
    }

    // 检查学院名称是否被其他学院使用
    const [nameConflict] = await pool.execute(
      'SELECT id FROM colleges WHERE name = ? AND id != ?',
      [name, id]
    );

    if (nameConflict.length > 0) {
      return errorResponse(res, '该学院名称已被使用', 409);
    }

    // 更新学院
    await pool.execute(
      'UPDATE colleges SET name = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [name, id]
    );

    successResponse(res, null, '学院更新成功');

  } catch (error) {
    console.error('更新学院错误:', error);
    errorResponse(res, '更新学院失败', 500, error);
  }
};

// 删除学院
export const deleteCollege = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查学院是否存在
    const [existingColleges] = await pool.execute('SELECT id FROM colleges WHERE id = ?', [id]);
    if (existingColleges.length === 0) {
      return errorResponse(res, '学院不存在', 404);
    }

    // 检查是否有专业关联到此学院
    const [relatedMajors] = await pool.execute('SELECT id FROM majors WHERE college_id = ?', [id]);
    if (relatedMajors.length > 0) {
      return errorResponse(res, '该学院下还有专业，无法删除', 400);
    }

    // 检查是否有用户关联到此学院
    const [relatedUsers] = await pool.execute('SELECT id FROM users WHERE college_id = ?', [id]);
    if (relatedUsers.length > 0) {
      return errorResponse(res, '该学院下还有用户，无法删除', 400);
    }

    // 删除学院
    await pool.execute('DELETE FROM colleges WHERE id = ?', [id]);

    successResponse(res, null, '学院删除成功');

  } catch (error) {
    console.error('删除学院错误:', error);
    errorResponse(res, '删除学院失败', 500, error);
  }
};

// 获取学院下的所有专业
export const getCollegeMajors = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查学院是否存在
    const [colleges] = await pool.execute('SELECT id FROM colleges WHERE id = ?', [id]);
    if (colleges.length === 0) {
      return errorResponse(res, '学院不存在', 404);
    }

    const [rows] = await pool.execute(
      'SELECT id, name, created_at FROM majors WHERE college_id = ? ORDER BY name',
      [id]
    );

    successResponse(res, { majors: rows });
  } catch (error) {
    console.error('获取学院专业列表错误:', error);
    errorResponse(res, '获取学院专业列表失败', 500, error);
  }
};
