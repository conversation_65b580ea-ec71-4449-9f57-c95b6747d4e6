-- 易宿管 - 智能宿舍管理系统数据库
-- 创建时间: 2025-06-15
-- 数据库名: redhat

-- 创建数据库
CREATE DATABASE IF NOT EXISTS redhat CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE redhat;

-- 1. 学院表
CREATE TABLE colleges (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 2. 专业表
CREATE TABLE majors (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    college_id VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE CASCADE,
    UNIQUE KEY unique_major_college (name, college_id)
);

-- 3. 宿舍楼表
CREATE TABLE dorm_buildings (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL UNIQUE,
    floors INT NOT NULL,
    total_rooms INT NOT NULL,
    assigned_admin_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 4. 用户表
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    role ENUM('系统管理员', '宿舍管理员', '学生', '维修人员') NOT NULL,
    college_id VARCHAR(50),
    major_id VARCHAR(50),
    dorm_building_id VARCHAR(50),
    room_number VARCHAR(20),
    emergency_contact_name VARCHAR(100),
    emergency_contact_phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE SET NULL,
    FOREIGN KEY (major_id) REFERENCES majors(id) ON DELETE SET NULL,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE SET NULL
);

-- 5. 房间表
CREATE TABLE rooms (
    id VARCHAR(50) PRIMARY KEY,
    room_number VARCHAR(20) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    floor INT NOT NULL,
    type ENUM('单人间', '双人间', '四人间', '六人间') NOT NULL,
    capacity INT NOT NULL,
    occupied_beds INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    UNIQUE KEY unique_room_building (room_number, dorm_building_id)
);

-- 6. 床位表
CREATE TABLE beds (
    id VARCHAR(50) PRIMARY KEY,
    room_id VARCHAR(50) NOT NULL,
    bed_number VARCHAR(10) NOT NULL,
    status ENUM('空闲', '已入住') DEFAULT '空闲',
    student_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY unique_bed_room (bed_number, room_id)
);

-- 7. 维修请求表
CREATE TABLE repair_requests (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    room_number VARCHAR(20) NOT NULL,
    dorm_building VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    image_url VARCHAR(500),
    contact VARCHAR(100) NOT NULL,
    status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') DEFAULT '待处理',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    assigned_to VARCHAR(50),
    assigned_to_name VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
);

-- 8. 维修更新记录表
CREATE TABLE repair_updates (
    id VARCHAR(50) PRIMARY KEY,
    repair_request_id VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100) NOT NULL,
    notes TEXT NOT NULL,
    new_status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消'),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (repair_request_id) REFERENCES repair_requests(id) ON DELETE CASCADE
);

-- 9. 公告表
CREATE TABLE announcements (
    id VARCHAR(50) PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    content TEXT NOT NULL,
    author_id VARCHAR(50) NOT NULL,
    author_name VARCHAR(100) NOT NULL,
    scope ENUM('All', 'College', 'DormBuilding') NOT NULL,
    target_id VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
);

-- 10. 水电费表
CREATE TABLE utility_bills (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    month VARCHAR(7) NOT NULL, -- YYYY-MM
    electricity_usage DECIMAL(10,2) NOT NULL,
    electricity_cost DECIMAL(10,2) NOT NULL,
    water_usage DECIMAL(10,2) NOT NULL,
    water_cost DECIMAL(10,2) NOT NULL,
    total_cost DECIMAL(10,2) NOT NULL,
    is_paid BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_month (student_id, month)
);

-- 11. 晚归记录表
CREATE TABLE late_returns (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    time TIME NOT NULL,
    reason TEXT NOT NULL,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 12. 访客记录表
CREATE TABLE visitors (
    id VARCHAR(50) PRIMARY KEY,
    visitor_name VARCHAR(100) NOT NULL,
    visitor_id_number VARCHAR(20) NOT NULL,
    reason TEXT NOT NULL,
    entry_time TIMESTAMP NOT NULL,
    exit_time TIMESTAMP,
    visited_student_id VARCHAR(50) NOT NULL,
    visited_student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (visited_student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 13. 违规记录表
CREATE TABLE violations (
    id VARCHAR(50) PRIMARY KEY,
    student_id VARCHAR(50) NOT NULL,
    student_name VARCHAR(100) NOT NULL,
    dorm_building_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    type VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    action_taken TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 14. 文明宿舍评分表
CREATE TABLE civilized_dorm_scores (
    id VARCHAR(50) PRIMARY KEY,
    dorm_building_id VARCHAR(50) NOT NULL,
    room_id VARCHAR(50) NOT NULL,
    date DATE NOT NULL,
    score INT NOT NULL CHECK (score >= 0 AND score <= 100),
    notes TEXT,
    recorded_by VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
    FOREIGN KEY (room_id) REFERENCES rooms(id) ON DELETE CASCADE,
    FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 添加外键约束到宿舍楼表
ALTER TABLE dorm_buildings ADD FOREIGN KEY (assigned_admin_id) REFERENCES users(id) ON DELETE SET NULL;

-- 创建索引以提高查询性能
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_repair_requests_status ON repair_requests(status);
CREATE INDEX idx_repair_requests_student ON repair_requests(student_id);
CREATE INDEX idx_announcements_scope ON announcements(scope);
CREATE INDEX idx_utility_bills_month ON utility_bills(month);
CREATE INDEX idx_late_returns_date ON late_returns(date);
CREATE INDEX idx_visitors_entry_time ON visitors(entry_time);
CREATE INDEX idx_violations_date ON violations(date);
CREATE INDEX idx_civilized_scores_date ON civilized_dorm_scores(date);

-- ========================================
-- 插入初始数据
-- ========================================

-- 插入学院数据
INSERT INTO colleges (id, name) VALUES
('col01', '工程学院'),
('col02', '文理学院'),
('col03', '商学院');

-- 插入专业数据
INSERT INTO majors (id, name, college_id) VALUES
('maj01', '计算机科学', 'col01'),
('maj02', '机械工程', 'col01'),
('maj03', '历史学', 'col02'),
('maj04', '文学', 'col02'),
('maj05', '金融学', 'col03');

-- 插入宿舍楼数据
INSERT INTO dorm_buildings (id, name, floors, total_rooms) VALUES
('bldgA', 'A栋 (阿尔法楼)', 5, 50),
('bldgB', 'B栋 (贝塔公寓)', 4, 40),
('bldgC', 'C栋 (伽马学舍)', 6, 60);

-- 插入用户数据 (密码使用bcrypt加密后的 'password123')
INSERT INTO users (id, name, email, password, role, phone, college_id, major_id, dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone) VALUES
('sysadmin01', '系统管理员用户', '<EMAIL>', '$2b$10$rOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQ', '系统管理员', NULL, NULL, NULL, NULL, NULL, NULL, NULL),
('dormadmin01', '张三 (A栋宿舍管理员)', '<EMAIL>', '$2b$10$rOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQ', '宿舍管理员', NULL, NULL, NULL, 'bldgA', NULL, NULL, NULL),
('dormadmin02', '李四 (B栋宿舍管理员)', '<EMAIL>', '$2b$10$rOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQ', '宿舍管理员', NULL, NULL, NULL, 'bldgB', NULL, NULL, NULL),
('student01', '王五', '<EMAIL>', '$2b$10$rOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQ', '学生', '13000000001', 'col01', 'maj01', 'bldgA', '101', '王大锤', '13800138000'),
('student02', '赵六', '<EMAIL>', '$2b$10$rOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQ', '学生', '13000000002', 'col02', 'maj03', 'bldgB', '205', '赵大力', '13800138001'),
('student03', '孙七', '<EMAIL>', '$2b$10$rOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQ', '学生', '13000000003', 'col01', 'maj01', 'bldgA', '102', '孙大山', '13800138002'),
('repair01', '维修工丁师傅', '<EMAIL>', '$2b$10$rOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQqQqQqQqOzJqQqQqQqQqQ', '维修人员', '13000000004', NULL, NULL, NULL, NULL, NULL, NULL);

-- 更新宿舍楼的管理员分配
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin01' WHERE id = 'bldgA';
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin02' WHERE id = 'bldgB';

-- 插入房间数据
INSERT INTO rooms (id, room_number, dorm_building_id, floor, type, capacity, occupied_beds) VALUES
('roomA101', '101', 'bldgA', 1, '四人间', 4, 1),
('roomA102', '102', 'bldgA', 1, '四人间', 4, 1),
('roomA103', '103', 'bldgA', 1, '四人间', 4, 0),
('roomA201', '201', 'bldgA', 2, '四人间', 4, 0),
('roomA202', '202', 'bldgA', 2, '四人间', 4, 0),
('roomB205', '205', 'bldgB', 2, '四人间', 4, 1),
('roomB206', '206', 'bldgB', 2, '四人间', 4, 0),
('roomB301', '301', 'bldgB', 3, '四人间', 4, 0);

-- 插入床位数据
INSERT INTO beds (id, room_id, bed_number, status, student_id) VALUES
-- A栋101房间床位
('bedA101_1', 'roomA101', '1', '已入住', 'student01'),
('bedA101_2', 'roomA101', '2', '空闲', NULL),
('bedA101_3', 'roomA101', '3', '空闲', NULL),
('bedA101_4', 'roomA101', '4', '空闲', NULL),
-- A栋102房间床位
('bedA102_1', 'roomA102', '1', '已入住', 'student03'),
('bedA102_2', 'roomA102', '2', '空闲', NULL),
('bedA102_3', 'roomA102', '3', '空闲', NULL),
('bedA102_4', 'roomA102', '4', '空闲', NULL),
-- A栋103房间床位
('bedA103_1', 'roomA103', '1', '空闲', NULL),
('bedA103_2', 'roomA103', '2', '空闲', NULL),
('bedA103_3', 'roomA103', '3', '空闲', NULL),
('bedA103_4', 'roomA103', '4', '空闲', NULL),
-- B栋205房间床位
('bedB205_1', 'roomB205', '1', '已入住', 'student02'),
('bedB205_2', 'roomB205', '2', '空闲', NULL),
('bedB205_3', 'roomB205', '3', '空闲', NULL),
('bedB205_4', 'roomB205', '4', '空闲', NULL);

-- 插入维修请求数据
INSERT INTO repair_requests (id, student_id, student_name, room_number, dorm_building, description, image_url, contact, status, submitted_at, assigned_to, assigned_to_name) VALUES
('repair001', 'student01', '王五', '101', 'A栋 (阿尔法楼)', '卫生间水龙头漏水严重，无法关闭。', 'https://picsum.photos/seed/tap/300/200', '<EMAIL>', '已指派', '2024-07-20 10:00:00', 'repair01', '维修工丁师傅'),
('repair002', 'student02', '赵六', '205', 'B栋 (贝塔公寓)', '空调不制冷，温度调节无效。', 'https://picsum.photos/seed/ac/300/200', '<EMAIL>', '待处理', '2024-07-21 14:30:00', NULL, NULL),
('repair003', 'student03', '孙七', '102', 'A栋 (阿尔法楼)', '门锁损坏，钥匙无法正常开启。', NULL, '<EMAIL>', '维修中', '2024-07-19 09:15:00', 'repair01', '维修工丁师傅');

-- 插入维修更新记录
INSERT INTO repair_updates (id, repair_request_id, timestamp, updated_by, notes, new_status) VALUES
('update001', 'repair001', '2024-07-20 11:00:00', '张三 (A栋宿舍管理员)', '已指派给丁师傅处理。', '已指派'),
('update002', 'repair003', '2024-07-19 10:00:00', '张三 (A栋宿舍管理员)', '已指派给丁师傅处理。', '已指派'),
('update003', 'repair003', '2024-07-19 15:30:00', '维修工丁师傅', '已到现场开始维修，预计明天完成。', '维修中');

-- 插入公告数据
INSERT INTO announcements (id, title, content, author_id, author_name, scope, target_id, created_at) VALUES
('ann001', '宿舍区域网络维护通知', '定于本周六晚上10点至次日凌晨6点进行网络设备维护，期间可能出现网络中断，请同学们提前做好准备。', 'sysadmin01', '系统管理员用户', 'All', NULL, '2024-07-18 09:00:00'),
('ann002', 'A栋热水供应时间调整', '从下周一开始，A栋热水供应时间调整为早上6:00-9:00，晚上18:00-23:00，请同学们合理安排用水时间。', 'dormadmin01', '张三 (A栋宿舍管理员)', 'DormBuilding', 'bldgA', '2024-07-19 16:30:00'),
('ann003', '工程学院新生入学指导', '工程学院新生请于下周三下午2点到学院报告厅参加入学指导会议，请准时参加。', 'sysadmin01', '系统管理员用户', 'College', 'col01', '2024-07-20 10:15:00');

-- 插入水电费数据
INSERT INTO utility_bills (id, student_id, room_id, month, electricity_usage, electricity_cost, water_usage, water_cost, total_cost, is_paid) VALUES
('bill001', 'student01', 'roomA101', '2024-06', 120.50, 60.25, 15.30, 45.90, 106.15, TRUE),
('bill002', 'student01', 'roomA101', '2024-07', 135.20, 67.60, 18.20, 54.60, 122.20, FALSE),
('bill003', 'student02', 'roomB205', '2024-06', 98.30, 49.15, 12.80, 38.40, 87.55, TRUE),
('bill004', 'student02', 'roomB205', '2024-07', 110.40, 55.20, 14.50, 43.50, 98.70, FALSE),
('bill005', 'student03', 'roomA102', '2024-06', 105.60, 52.80, 13.90, 41.70, 94.50, TRUE),
('bill006', 'student03', 'roomA102', '2024-07', 118.90, 59.45, 16.20, 48.60, 108.05, FALSE);

-- 插入晚归记录数据
INSERT INTO late_returns (id, student_id, student_name, dorm_building_id, date, time, reason, recorded_by) VALUES
('late001', 'student01', '王五', 'bldgA', '2024-07-15', '23:30:00', '参加社团活动延迟', 'dormadmin01'),
('late002', 'student02', '赵六', 'bldgB', '2024-07-16', '23:45:00', '图书馆学习到很晚', 'dormadmin02'),
('late003', 'student03', '孙七', 'bldgA', '2024-07-17', '00:15:00', '和朋友聚餐', 'dormadmin01');

-- 插入访客记录数据
INSERT INTO visitors (id, visitor_name, visitor_id_number, reason, entry_time, exit_time, visited_student_id, visited_student_name, dorm_building_id, recorded_by) VALUES
('vis001', '访客甲', '123456789012345678', '探亲', '2024-07-20 14:00:00', '2024-07-20 16:00:00', 'student01', '王五', 'bldgA', 'dormadmin01'),
('vis002', '访客乙', '987654321098765432', '送东西', '2024-07-21 10:00:00', NULL, 'student02', '赵六', 'bldgB', 'dormadmin02'),
('vis003', '访客丙', '456789123456789012', '学习讨论', '2024-07-19 19:00:00', '2024-07-19 21:30:00', 'student03', '孙七', 'bldgA', 'dormadmin01');

-- 插入违规记录数据
INSERT INTO violations (id, student_id, student_name, dorm_building_id, date, type, description, action_taken, recorded_by) VALUES
('vio001', 'student01', '王五', 'bldgA', '2024-07-18', '噪音投诉', '深夜播放音乐声音过大，影响其他同学休息', '口头警告，要求整改', 'dormadmin01'),
('vio002', 'student02', '赵六', 'bldgB', '2024-07-19', '违禁物品', '在宿舍内使用大功率电器（电磁炉）', '没收违禁物品，书面警告', 'dormadmin02');

-- 插入文明宿舍评分数据
INSERT INTO civilized_dorm_scores (id, dorm_building_id, room_id, date, score, notes, recorded_by) VALUES
('score001', 'bldgA', 'roomA101', '2024-07-15', 85, '卫生状况良好，物品摆放整齐', 'dormadmin01'),
('score002', 'bldgA', 'roomA102', '2024-07-15', 92, '卫生优秀，装饰美观', 'dormadmin01'),
('score003', 'bldgA', 'roomA103', '2024-07-15', 78, '卫生一般，需要改进', 'dormadmin01'),
('score004', 'bldgB', 'roomB205', '2024-07-16', 88, '整体不错，继续保持', 'dormadmin02'),
('score005', 'bldgB', 'roomB206', '2024-07-16', 95, '非常优秀，值得表扬', 'dormadmin02');

-- 数据库初始化完成
-- 使用说明：
-- 1. 默认管理员账号：<EMAIL> / password123
-- 2. 宿舍管理员账号：<EMAIL> / password123 (A栋)
-- 3. 宿舍管理员账号：<EMAIL> / password123 (B栋)
-- 4. 学生账号：<EMAIL> / password123 (王五)
-- 5. 维修人员账号：<EMAIL> / password123
