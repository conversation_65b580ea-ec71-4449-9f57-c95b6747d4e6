import { pool } from '../config/database.js';
import { successResponse, errorResponse } from '../utils/response.js';

// 获取公告列表
export const getAllAnnouncements = async (req, res) => {
  try {
    const currentUser = req.user;
    const { scope, limit = 50 } = req.query;

    let query = `
      SELECT a.*, c.name as college_name, db.name as dorm_building_name
      FROM announcements a
      LEFT JOIN colleges c ON a.target_id = c.id AND a.scope = 'College'
      LEFT JOIN dorm_buildings db ON a.target_id = db.id AND a.scope = 'DormBuilding'
      WHERE 1=1
    `;
    
    const params = [];

    // 根据用户角色过滤公告
    if (currentUser.role === '学生') {
      // 学生可以看到：全部公告、自己学院的公告、自己宿舍楼的公告
      query += ` AND (
        a.scope = 'All' 
        OR (a.scope = 'College' AND a.target_id = ?)
        OR (a.scope = 'DormBuilding' AND a.target_id = ?)
      )`;
      params.push(currentUser.college_id, currentUser.dorm_building_id);
    } else if (currentUser.role === '宿舍管理员') {
      // 宿舍管理员可以看到：全部公告、自己管理的宿舍楼公告
      query += ` AND (
        a.scope = 'All'
        OR (a.scope = 'DormBuilding' AND a.target_id = ?)
      )`;
      params.push(currentUser.dorm_building_id);
    }
    // 系统管理员和维修人员可以看到所有公告

    // 添加范围过滤
    if (scope) {
      query += ' AND a.scope = ?';
      params.push(scope);
    }

    query += ' ORDER BY a.created_at DESC LIMIT ?';
    params.push(parseInt(limit));

    const [rows] = await pool.execute(query, params);

    successResponse(res, { announcements: rows });
  } catch (error) {
    console.error('获取公告列表错误:', error);
    errorResponse(res, '获取公告列表失败', 500, error);
  }
};

// 根据ID获取公告
export const getAnnouncementById = async (req, res) => {
  try {
    const { id } = req.params;

    const [rows] = await pool.execute(
      `SELECT a.*, c.name as college_name, db.name as dorm_building_name
       FROM announcements a
       LEFT JOIN colleges c ON a.target_id = c.id AND a.scope = 'College'
       LEFT JOIN dorm_buildings db ON a.target_id = db.id AND a.scope = 'DormBuilding'
       WHERE a.id = ?`,
      [id]
    );

    if (rows.length === 0) {
      return errorResponse(res, '公告不存在', 404);
    }

    successResponse(res, { announcement: rows[0] });
  } catch (error) {
    console.error('获取公告详情错误:', error);
    errorResponse(res, '获取公告详情失败', 500, error);
  }
};

// 创建公告
export const createAnnouncement = async (req, res) => {
  try {
    const { title, content, scope, targetId } = req.body;
    const currentUser = req.user;

    // 只有管理员可以创建公告
    if (!['系统管理员', '宿舍管理员'].includes(currentUser.role)) {
      return errorResponse(res, '无权限创建公告', 403);
    }

    if (!title || !content || !scope) {
      return errorResponse(res, '标题、内容和范围不能为空', 400);
    }

    // 验证范围和目标ID
    if (scope === 'College' && !targetId) {
      return errorResponse(res, '学院范围的公告必须指定目标学院', 400);
    }

    if (scope === 'DormBuilding' && !targetId) {
      return errorResponse(res, '宿舍楼范围的公告必须指定目标宿舍楼', 400);
    }

    // 宿舍管理员只能创建全部范围或自己管理的宿舍楼范围的公告
    if (currentUser.role === '宿舍管理员') {
      if (scope === 'College') {
        return errorResponse(res, '宿舍管理员无权创建学院范围的公告', 403);
      }
      if (scope === 'DormBuilding' && targetId !== currentUser.dorm_building_id) {
        return errorResponse(res, '宿舍管理员只能创建自己管理的宿舍楼公告', 403);
      }
    }

    // 验证目标ID的有效性
    if (scope === 'College' && targetId) {
      const [colleges] = await pool.execute('SELECT id FROM colleges WHERE id = ?', [targetId]);
      if (colleges.length === 0) {
        return errorResponse(res, '指定的学院不存在', 404);
      }
    }

    if (scope === 'DormBuilding' && targetId) {
      const [buildings] = await pool.execute('SELECT id FROM dorm_buildings WHERE id = ?', [targetId]);
      if (buildings.length === 0) {
        return errorResponse(res, '指定的宿舍楼不存在', 404);
      }
    }

    // 生成公告ID
    const announcementId = 'ann_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 插入公告
    await pool.execute(
      'INSERT INTO announcements (id, title, content, author_id, author_name, scope, target_id) VALUES (?, ?, ?, ?, ?, ?, ?)',
      [announcementId, title, content, currentUser.id, currentUser.name, scope, targetId || null]
    );

    successResponse(res, { announcementId }, '公告创建成功', 201);

  } catch (error) {
    console.error('创建公告错误:', error);
    errorResponse(res, '创建公告失败', 500, error);
  }
};

// 更新公告
export const updateAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, scope, targetId } = req.body;
    const currentUser = req.user;

    // 检查公告是否存在
    const [existingAnnouncements] = await pool.execute('SELECT * FROM announcements WHERE id = ?', [id]);
    if (existingAnnouncements.length === 0) {
      return errorResponse(res, '公告不存在', 404);
    }

    const announcement = existingAnnouncements[0];

    // 权限检查：只有公告作者或系统管理员可以更新
    if (currentUser.role !== '系统管理员' && announcement.author_id !== currentUser.id) {
      return errorResponse(res, '无权限更新此公告', 403);
    }

    // 验证范围和目标ID
    if (scope === 'College' && !targetId) {
      return errorResponse(res, '学院范围的公告必须指定目标学院', 400);
    }

    if (scope === 'DormBuilding' && !targetId) {
      return errorResponse(res, '宿舍楼范围的公告必须指定目标宿舍楼', 400);
    }

    // 宿舍管理员权限检查
    if (currentUser.role === '宿舍管理员') {
      if (scope === 'College') {
        return errorResponse(res, '宿舍管理员无权创建学院范围的公告', 403);
      }
      if (scope === 'DormBuilding' && targetId !== currentUser.dorm_building_id) {
        return errorResponse(res, '宿舍管理员只能创建自己管理的宿舍楼公告', 403);
      }
    }

    // 验证目标ID的有效性
    if (scope === 'College' && targetId) {
      const [colleges] = await pool.execute('SELECT id FROM colleges WHERE id = ?', [targetId]);
      if (colleges.length === 0) {
        return errorResponse(res, '指定的学院不存在', 404);
      }
    }

    if (scope === 'DormBuilding' && targetId) {
      const [buildings] = await pool.execute('SELECT id FROM dorm_buildings WHERE id = ?', [targetId]);
      if (buildings.length === 0) {
        return errorResponse(res, '指定的宿舍楼不存在', 404);
      }
    }

    // 更新公告
    await pool.execute(
      `UPDATE announcements SET 
       title = COALESCE(?, title),
       content = COALESCE(?, content),
       scope = COALESCE(?, scope),
       target_id = ?,
       updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [title, content, scope, targetId || null, id]
    );

    successResponse(res, null, '公告更新成功');

  } catch (error) {
    console.error('更新公告错误:', error);
    errorResponse(res, '更新公告失败', 500, error);
  }
};

// 删除公告
export const deleteAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // 检查公告是否存在
    const [existingAnnouncements] = await pool.execute('SELECT * FROM announcements WHERE id = ?', [id]);
    if (existingAnnouncements.length === 0) {
      return errorResponse(res, '公告不存在', 404);
    }

    const announcement = existingAnnouncements[0];

    // 权限检查：只有公告作者或系统管理员可以删除
    if (currentUser.role !== '系统管理员' && announcement.author_id !== currentUser.id) {
      return errorResponse(res, '无权限删除此公告', 403);
    }

    // 删除公告
    await pool.execute('DELETE FROM announcements WHERE id = ?', [id]);

    successResponse(res, null, '公告删除成功');

  } catch (error) {
    console.error('删除公告错误:', error);
    errorResponse(res, '删除公告失败', 500, error);
  }
};
