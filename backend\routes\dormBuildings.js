import express from 'express';
import {
  getAllDormBuildings,
  getDormBuildingById,
  createDormBuilding,
  updateDormBuilding,
  deleteDormBuilding,
  getDormBuildingRooms
} from '../controllers/dormBuildingController.js';
import { authenticateToken, requireSystemAdmin } from '../middleware/auth.js';

const router = express.Router();

// 所有路由都需要认证
router.use(authenticateToken);

// 获取所有宿舍楼 - 所有用户都可以访问
router.get('/', getAllDormBuildings);

// 获取宿舍楼下的房间 - 所有用户都可以访问
router.get('/:id/rooms', getDormBuildingRooms);

// 根据ID获取宿舍楼 - 所有用户都可以访问
router.get('/:id', getDormBuildingById);

// 创建宿舍楼 - 仅系统管理员
router.post('/', requireSystemAdmin, createDormBuilding);

// 更新宿舍楼 - 仅系统管理员
router.put('/:id', requireSystemAdmin, updateDormBuilding);

// 删除宿舍楼 - 仅系统管理员
router.delete('/:id', requireSystemAdmin, deleteDormBuilding);

export default router;
