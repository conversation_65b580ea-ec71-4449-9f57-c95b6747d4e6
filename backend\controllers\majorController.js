import { pool } from '../config/database.js';
import { successResponse, errorResponse } from '../utils/response.js';

// 获取所有专业
export const getAllMajors = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT m.id, m.name, m.college_id, c.name as college_name, m.created_at 
       FROM majors m 
       LEFT JOIN colleges c ON m.college_id = c.id 
       ORDER BY c.name, m.name`
    );

    successResponse(res, { majors: rows });
  } catch (error) {
    console.error('获取专业列表错误:', error);
    errorResponse(res, '获取专业列表失败', 500, error);
  }
};

// 根据ID获取专业
export const getMajorById = async (req, res) => {
  try {
    const { id } = req.params;

    const [rows] = await pool.execute(
      `SELECT m.*, c.name as college_name 
       FROM majors m 
       LEFT JOIN colleges c ON m.college_id = c.id 
       WHERE m.id = ?`,
      [id]
    );

    if (rows.length === 0) {
      return errorResponse(res, '专业不存在', 404);
    }

    successResponse(res, { major: rows[0] });
  } catch (error) {
    console.error('获取专业详情错误:', error);
    errorResponse(res, '获取专业详情失败', 500, error);
  }
};

// 创建专业
export const createMajor = async (req, res) => {
  try {
    const { name, college_id } = req.body;

    if (!name || !college_id) {
      return errorResponse(res, '专业名称和学院ID不能为空', 400);
    }

    // 检查学院是否存在
    const [colleges] = await pool.execute('SELECT id FROM colleges WHERE id = ?', [college_id]);
    if (colleges.length === 0) {
      return errorResponse(res, '指定的学院不存在', 404);
    }

    // 检查专业名称在该学院下是否已存在
    const [existingMajors] = await pool.execute(
      'SELECT id FROM majors WHERE name = ? AND college_id = ?',
      [name, college_id]
    );

    if (existingMajors.length > 0) {
      return errorResponse(res, '该学院下已存在同名专业', 409);
    }

    // 生成专业ID
    const majorId = 'maj_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 插入专业
    await pool.execute(
      'INSERT INTO majors (id, name, college_id) VALUES (?, ?, ?)',
      [majorId, name, college_id]
    );

    successResponse(res, { majorId }, '专业创建成功', 201);

  } catch (error) {
    console.error('创建专业错误:', error);
    errorResponse(res, '创建专业失败', 500, error);
  }
};

// 更新专业
export const updateMajor = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, college_id } = req.body;

    if (!name || !college_id) {
      return errorResponse(res, '专业名称和学院ID不能为空', 400);
    }

    // 检查专业是否存在
    const [existingMajors] = await pool.execute('SELECT id FROM majors WHERE id = ?', [id]);
    if (existingMajors.length === 0) {
      return errorResponse(res, '专业不存在', 404);
    }

    // 检查学院是否存在
    const [colleges] = await pool.execute('SELECT id FROM colleges WHERE id = ?', [college_id]);
    if (colleges.length === 0) {
      return errorResponse(res, '指定的学院不存在', 404);
    }

    // 检查专业名称在该学院下是否被其他专业使用
    const [nameConflict] = await pool.execute(
      'SELECT id FROM majors WHERE name = ? AND college_id = ? AND id != ?',
      [name, college_id, id]
    );

    if (nameConflict.length > 0) {
      return errorResponse(res, '该学院下已存在同名专业', 409);
    }

    // 更新专业
    await pool.execute(
      'UPDATE majors SET name = ?, college_id = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [name, college_id, id]
    );

    successResponse(res, null, '专业更新成功');

  } catch (error) {
    console.error('更新专业错误:', error);
    errorResponse(res, '更新专业失败', 500, error);
  }
};

// 删除专业
export const deleteMajor = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查专业是否存在
    const [existingMajors] = await pool.execute('SELECT id FROM majors WHERE id = ?', [id]);
    if (existingMajors.length === 0) {
      return errorResponse(res, '专业不存在', 404);
    }

    // 检查是否有用户关联到此专业
    const [relatedUsers] = await pool.execute('SELECT id FROM users WHERE major_id = ?', [id]);
    if (relatedUsers.length > 0) {
      return errorResponse(res, '该专业下还有用户，无法删除', 400);
    }

    // 删除专业
    await pool.execute('DELETE FROM majors WHERE id = ?', [id]);

    successResponse(res, null, '专业删除成功');

  } catch (error) {
    console.error('删除专业错误:', error);
    errorResponse(res, '删除专业失败', 500, error);
  }
};

// 根据学院ID获取专业列表
export const getMajorsByCollege = async (req, res) => {
  try {
    const { college_id } = req.params;

    // 检查学院是否存在
    const [colleges] = await pool.execute('SELECT id FROM colleges WHERE id = ?', [college_id]);
    if (colleges.length === 0) {
      return errorResponse(res, '学院不存在', 404);
    }

    const [rows] = await pool.execute(
      'SELECT id, name, created_at FROM majors WHERE college_id = ? ORDER BY name',
      [college_id]
    );

    successResponse(res, { majors: rows });
  } catch (error) {
    console.error('获取学院专业列表错误:', error);
    errorResponse(res, '获取学院专业列表失败', 500, error);
  }
};
