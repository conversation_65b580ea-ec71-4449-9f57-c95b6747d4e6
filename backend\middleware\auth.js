import jwt from 'jsonwebtoken';
import { pool } from '../config/database.js';

// JWT认证中间件
export const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '访问令牌缺失'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 从数据库获取用户信息
    const [rows] = await pool.execute(
      'SELECT id, name, email, role, college_id, major_id, dorm_building_id, room_number FROM users WHERE id = ?',
      [decoded.userId]
    );

    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    req.user = rows[0];
    next();
  } catch (error) {
    console.error('JWT验证错误:', error);
    return res.status(403).json({
      success: false,
      message: '访问令牌无效'
    });
  }
};

// 角色权限检查中间件
export const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};

// 系统管理员权限
export const requireSystemAdmin = requireRole(['系统管理员']);

// 宿舍管理员权限
export const requireDormAdmin = requireRole(['宿舍管理员']);

// 学生权限
export const requireStudent = requireRole(['学生']);

// 维修人员权限
export const requireRepairStaff = requireRole(['维修人员']);

// 管理员权限（系统管理员或宿舍管理员）
export const requireAdmin = requireRole(['系统管理员', '宿舍管理员']);
