// API配置文件
export const API_BASE_URL = 'http://localhost:3001/api';

// API端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    ME: '/auth/me',
    CHANGE_PASSWORD: '/auth/change-password'
  },
  
  // 用户管理
  USERS: {
    BASE: '/users',
    DORM_ADMINS: '/users/dorm-admins',
    REPAIR_STAFF: '/users/repair-staff'
  },
  
  // 学院管理
  COLLEGES: {
    BASE: '/colleges'
  },
  
  // 专业管理
  MAJORS: {
    BASE: '/majors',
    BY_COLLEGE: '/majors/college'
  },
  
  // 宿舍楼管理
  DORM_BUILDINGS: {
    BASE: '/dorm-buildings'
  },
  
  // 维修请求
  REPAIRS: {
    BASE: '/repairs',
    ASSIGN: '/repairs/{id}/assign',
    STATUS: '/repairs/{id}/status'
  },
  
  // 公告管理
  ANNOUNCEMENTS: {
    BASE: '/announcements'
  }
};

// HTTP请求工具函数
export const apiRequest = async (endpoint: string, options: RequestInit = {}) => {
  const token = localStorage.getItem('token');
  
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` })
    }
  };

  const mergedOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers
    }
  };

  try {
    const response = await fetch(`${API_BASE_URL}${endpoint}`, mergedOptions);
    
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
};

// GET请求
export const apiGet = (endpoint: string) => apiRequest(endpoint, { method: 'GET' });

// POST请求
export const apiPost = (endpoint: string, data: any) => 
  apiRequest(endpoint, {
    method: 'POST',
    body: JSON.stringify(data)
  });

// PUT请求
export const apiPut = (endpoint: string, data: any) => 
  apiRequest(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data)
  });

// DELETE请求
export const apiDelete = (endpoint: string) => 
  apiRequest(endpoint, { method: 'DELETE' });
