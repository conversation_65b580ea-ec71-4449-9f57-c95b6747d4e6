import { pool } from '../config/database.js';
import { successResponse, errorResponse } from '../utils/response.js';

// 获取所有宿舍楼
export const getAllDormBuildings = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT db.*, u.name as assigned_admin_name 
       FROM dorm_buildings db 
       LEFT JOIN users u ON db.assigned_admin_id = u.id 
       ORDER BY db.name`
    );

    successResponse(res, { dormBuildings: rows });
  } catch (error) {
    console.error('获取宿舍楼列表错误:', error);
    errorResponse(res, '获取宿舍楼列表失败', 500, error);
  }
};

// 根据ID获取宿舍楼
export const getDormBuildingById = async (req, res) => {
  try {
    const { id } = req.params;

    const [rows] = await pool.execute(
      `SELECT db.*, u.name as assigned_admin_name 
       FROM dorm_buildings db 
       LEFT JOIN users u ON db.assigned_admin_id = u.id 
       WHERE db.id = ?`,
      [id]
    );

    if (rows.length === 0) {
      return errorResponse(res, '宿舍楼不存在', 404);
    }

    successResponse(res, { dormBuilding: rows[0] });
  } catch (error) {
    console.error('获取宿舍楼详情错误:', error);
    errorResponse(res, '获取宿舍楼详情失败', 500, error);
  }
};

// 创建宿舍楼
export const createDormBuilding = async (req, res) => {
  try {
    const { name, floors, totalRooms, assignedAdminId } = req.body;

    if (!name || !floors || !totalRooms) {
      return errorResponse(res, '宿舍楼名称、楼层数和总房间数不能为空', 400);
    }

    // 检查宿舍楼名称是否已存在
    const [existingBuildings] = await pool.execute(
      'SELECT id FROM dorm_buildings WHERE name = ?',
      [name]
    );

    if (existingBuildings.length > 0) {
      return errorResponse(res, '该宿舍楼名称已存在', 409);
    }

    // 如果指定了管理员，检查管理员是否存在且为宿舍管理员
    if (assignedAdminId) {
      const [admins] = await pool.execute(
        'SELECT id FROM users WHERE id = ? AND role = ?',
        [assignedAdminId, '宿舍管理员']
      );

      if (admins.length === 0) {
        return errorResponse(res, '指定的管理员不存在或不是宿舍管理员', 400);
      }
    }

    // 生成宿舍楼ID
    const buildingId = 'bldg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 插入宿舍楼
    await pool.execute(
      'INSERT INTO dorm_buildings (id, name, floors, total_rooms, assigned_admin_id) VALUES (?, ?, ?, ?, ?)',
      [buildingId, name, floors, totalRooms, assignedAdminId || null]
    );

    successResponse(res, { buildingId }, '宿舍楼创建成功', 201);

  } catch (error) {
    console.error('创建宿舍楼错误:', error);
    errorResponse(res, '创建宿舍楼失败', 500, error);
  }
};

// 更新宿舍楼
export const updateDormBuilding = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, floors, totalRooms, assignedAdminId } = req.body;

    // 检查宿舍楼是否存在
    const [existingBuildings] = await pool.execute('SELECT id FROM dorm_buildings WHERE id = ?', [id]);
    if (existingBuildings.length === 0) {
      return errorResponse(res, '宿舍楼不存在', 404);
    }

    // 检查宿舍楼名称是否被其他宿舍楼使用
    if (name) {
      const [nameConflict] = await pool.execute(
        'SELECT id FROM dorm_buildings WHERE name = ? AND id != ?',
        [name, id]
      );

      if (nameConflict.length > 0) {
        return errorResponse(res, '该宿舍楼名称已被使用', 409);
      }
    }

    // 如果指定了管理员，检查管理员是否存在且为宿舍管理员
    if (assignedAdminId) {
      const [admins] = await pool.execute(
        'SELECT id FROM users WHERE id = ? AND role = ?',
        [assignedAdminId, '宿舍管理员']
      );

      if (admins.length === 0) {
        return errorResponse(res, '指定的管理员不存在或不是宿舍管理员', 400);
      }
    }

    // 更新宿舍楼
    await pool.execute(
      `UPDATE dorm_buildings SET 
       name = COALESCE(?, name),
       floors = COALESCE(?, floors),
       total_rooms = COALESCE(?, total_rooms),
       assigned_admin_id = ?,
       updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [name, floors, totalRooms, assignedAdminId || null, id]
    );

    successResponse(res, null, '宿舍楼更新成功');

  } catch (error) {
    console.error('更新宿舍楼错误:', error);
    errorResponse(res, '更新宿舍楼失败', 500, error);
  }
};

// 删除宿舍楼
export const deleteDormBuilding = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查宿舍楼是否存在
    const [existingBuildings] = await pool.execute('SELECT id FROM dorm_buildings WHERE id = ?', [id]);
    if (existingBuildings.length === 0) {
      return errorResponse(res, '宿舍楼不存在', 404);
    }

    // 检查是否有房间关联到此宿舍楼
    const [relatedRooms] = await pool.execute('SELECT id FROM rooms WHERE dorm_building_id = ?', [id]);
    if (relatedRooms.length > 0) {
      return errorResponse(res, '该宿舍楼下还有房间，无法删除', 400);
    }

    // 检查是否有用户关联到此宿舍楼
    const [relatedUsers] = await pool.execute('SELECT id FROM users WHERE dorm_building_id = ?', [id]);
    if (relatedUsers.length > 0) {
      return errorResponse(res, '该宿舍楼下还有用户，无法删除', 400);
    }

    // 删除宿舍楼
    await pool.execute('DELETE FROM dorm_buildings WHERE id = ?', [id]);

    successResponse(res, null, '宿舍楼删除成功');

  } catch (error) {
    console.error('删除宿舍楼错误:', error);
    errorResponse(res, '删除宿舍楼失败', 500, error);
  }
};

// 获取宿舍楼下的所有房间
export const getDormBuildingRooms = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查宿舍楼是否存在
    const [buildings] = await pool.execute('SELECT id FROM dorm_buildings WHERE id = ?', [id]);
    if (buildings.length === 0) {
      return errorResponse(res, '宿舍楼不存在', 404);
    }

    const [rows] = await pool.execute(
      'SELECT * FROM rooms WHERE dorm_building_id = ? ORDER BY floor, room_number',
      [id]
    );

    successResponse(res, { rooms: rows });
  } catch (error) {
    console.error('获取宿舍楼房间列表错误:', error);
    errorResponse(res, '获取宿舍楼房间列表失败', 500, error);
  }
};
