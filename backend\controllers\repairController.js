import { pool } from '../config/database.js';
import { successResponse, errorResponse } from '../utils/response.js';

// 获取维修请求列表
export const getAllRepairRequests = async (req, res) => {
  try {
    const { status, studentId, assignedTo } = req.query;
    const currentUser = req.user;

    let query = `
      SELECT rr.*, 
             GROUP_CONCAT(
               JSON_OBJECT(
                 'id', ru.id,
                 'timestamp', ru.timestamp,
                 'updatedBy', ru.updated_by,
                 'notes', ru.notes,
                 'newStatus', ru.new_status
               ) ORDER BY ru.timestamp DESC
             ) as updates
      FROM repair_requests rr
      LEFT JOIN repair_updates ru ON rr.id = ru.repair_request_id
    `;
    
    const conditions = [];
    const params = [];

    // 根据用户角色过滤数据
    if (currentUser.role === '学生') {
      conditions.push('rr.student_id = ?');
      params.push(currentUser.id);
    } else if (currentUser.role === '维修人员') {
      conditions.push('(rr.assigned_to = ? OR rr.assigned_to IS NULL)');
      params.push(currentUser.id);
    } else if (currentUser.role === '宿舍管理员') {
      // 宿舍管理员只能看到自己管理的宿舍楼的维修请求
      conditions.push('rr.dorm_building = (SELECT name FROM dorm_buildings WHERE assigned_admin_id = ?)');
      params.push(currentUser.id);
    }

    // 添加其他过滤条件
    if (status) {
      conditions.push('rr.status = ?');
      params.push(status);
    }

    if (studentId) {
      conditions.push('rr.student_id = ?');
      params.push(studentId);
    }

    if (assignedTo) {
      conditions.push('rr.assigned_to = ?');
      params.push(assignedTo);
    }

    if (conditions.length > 0) {
      query += ' WHERE ' + conditions.join(' AND ');
    }

    query += ' GROUP BY rr.id ORDER BY rr.submitted_at DESC';

    const [rows] = await pool.execute(query, params);

    // 处理更新记录
    const repairRequests = rows.map(row => {
      let updates = [];
      if (row.updates) {
        try {
          updates = JSON.parse(`[${row.updates}]`);
        } catch (e) {
          updates = [];
        }
      }
      
      return {
        ...row,
        updates
      };
    });

    successResponse(res, { repairRequests });
  } catch (error) {
    console.error('获取维修请求列表错误:', error);
    errorResponse(res, '获取维修请求列表失败', 500, error);
  }
};

// 根据ID获取维修请求
export const getRepairRequestById = async (req, res) => {
  try {
    const { id } = req.params;

    const [rows] = await pool.execute(
      'SELECT * FROM repair_requests WHERE id = ?',
      [id]
    );

    if (rows.length === 0) {
      return errorResponse(res, '维修请求不存在', 404);
    }

    // 获取更新记录
    const [updates] = await pool.execute(
      'SELECT * FROM repair_updates WHERE repair_request_id = ? ORDER BY timestamp DESC',
      [id]
    );

    const repairRequest = {
      ...rows[0],
      updates
    };

    successResponse(res, { repairRequest });
  } catch (error) {
    console.error('获取维修请求详情错误:', error);
    errorResponse(res, '获取维修请求详情失败', 500, error);
  }
};

// 创建维修请求
export const createRepairRequest = async (req, res) => {
  try {
    const {
      description,
      imageUrl,
      contact
    } = req.body;

    const currentUser = req.user;

    // 只有学生可以创建维修请求
    if (currentUser.role !== '学生') {
      return errorResponse(res, '只有学生可以提交维修请求', 403);
    }

    if (!description || !contact) {
      return errorResponse(res, '描述和联系方式不能为空', 400);
    }

    // 获取学生的宿舍信息
    const [studentInfo] = await pool.execute(
      `SELECT u.name, u.room_number, db.name as dorm_building_name
       FROM users u
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
       WHERE u.id = ?`,
      [currentUser.id]
    );

    if (studentInfo.length === 0 || !studentInfo[0].room_number) {
      return errorResponse(res, '学生宿舍信息不完整，无法提交维修请求', 400);
    }

    const student = studentInfo[0];

    // 生成维修请求ID
    const repairId = 'repair_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

    // 插入维修请求
    await pool.execute(
      `INSERT INTO repair_requests (id, student_id, student_name, room_number, dorm_building, 
       description, image_url, contact, status, submitted_at) 
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())`,
      [
        repairId, currentUser.id, student.name, student.room_number,
        student.dorm_building_name, description, imageUrl, contact, '待处理'
      ]
    );

    successResponse(res, { repairId }, '维修请求提交成功', 201);

  } catch (error) {
    console.error('创建维修请求错误:', error);
    errorResponse(res, '创建维修请求失败', 500, error);
  }
};

// 更新维修请求状态
export const updateRepairRequestStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, assignedTo, assignedToName, notes } = req.body;

    const currentUser = req.user;

    // 检查维修请求是否存在
    const [existingRequests] = await pool.execute('SELECT * FROM repair_requests WHERE id = ?', [id]);
    if (existingRequests.length === 0) {
      return errorResponse(res, '维修请求不存在', 404);
    }

    const repairRequest = existingRequests[0];

    // 权限检查
    if (currentUser.role === '学生' && repairRequest.student_id !== currentUser.id) {
      return errorResponse(res, '无权限操作此维修请求', 403);
    }

    // 更新维修请求
    const updateFields = [];
    const updateParams = [];

    if (status) {
      updateFields.push('status = ?');
      updateParams.push(status);
    }

    if (assignedTo !== undefined) {
      updateFields.push('assigned_to = ?');
      updateParams.push(assignedTo);
    }

    if (assignedToName !== undefined) {
      updateFields.push('assigned_to_name = ?');
      updateParams.push(assignedToName);
    }

    if (updateFields.length > 0) {
      updateFields.push('updated_at = CURRENT_TIMESTAMP');
      updateParams.push(id);

      await pool.execute(
        `UPDATE repair_requests SET ${updateFields.join(', ')} WHERE id = ?`,
        updateParams
      );
    }

    // 添加更新记录
    if (notes || status) {
      const updateId = 'update_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
      
      await pool.execute(
        'INSERT INTO repair_updates (id, repair_request_id, updated_by, notes, new_status) VALUES (?, ?, ?, ?, ?)',
        [updateId, id, currentUser.name, notes || '', status || null]
      );
    }

    successResponse(res, null, '维修请求更新成功');

  } catch (error) {
    console.error('更新维修请求错误:', error);
    errorResponse(res, '更新维修请求失败', 500, error);
  }
};

// 删除维修请求
export const deleteRepairRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const currentUser = req.user;

    // 检查维修请求是否存在
    const [existingRequests] = await pool.execute('SELECT * FROM repair_requests WHERE id = ?', [id]);
    if (existingRequests.length === 0) {
      return errorResponse(res, '维修请求不存在', 404);
    }

    const repairRequest = existingRequests[0];

    // 权限检查：只有系统管理员或请求的学生本人可以删除
    if (currentUser.role !== '系统管理员' && repairRequest.student_id !== currentUser.id) {
      return errorResponse(res, '无权限删除此维修请求', 403);
    }

    // 删除维修请求（会级联删除相关的更新记录）
    await pool.execute('DELETE FROM repair_requests WHERE id = ?', [id]);

    successResponse(res, null, '维修请求删除成功');

  } catch (error) {
    console.error('删除维修请求错误:', error);
    errorResponse(res, '删除维修请求失败', 500, error);
  }
};

// 指派维修请求给维修人员
export const assignRepairRequest = async (req, res) => {
  try {
    const { id } = req.params;
    const { assignedTo } = req.body;

    const currentUser = req.user;

    // 只有管理员可以指派维修请求
    if (!['系统管理员', '宿舍管理员'].includes(currentUser.role)) {
      return errorResponse(res, '无权限指派维修请求', 403);
    }

    // 检查维修请求是否存在
    const [existingRequests] = await pool.execute('SELECT * FROM repair_requests WHERE id = ?', [id]);
    if (existingRequests.length === 0) {
      return errorResponse(res, '维修请求不存在', 404);
    }

    // 检查维修人员是否存在
    const [repairStaff] = await pool.execute(
      'SELECT id, name FROM users WHERE id = ? AND role = ?',
      [assignedTo, '维修人员']
    );

    if (repairStaff.length === 0) {
      return errorResponse(res, '指定的维修人员不存在', 404);
    }

    // 更新维修请求
    await pool.execute(
      'UPDATE repair_requests SET assigned_to = ?, assigned_to_name = ?, status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [assignedTo, repairStaff[0].name, '已指派', id]
    );

    // 添加更新记录
    const updateId = 'update_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    await pool.execute(
      'INSERT INTO repair_updates (id, repair_request_id, updated_by, notes, new_status) VALUES (?, ?, ?, ?, ?)',
      [updateId, id, currentUser.name, `已指派给${repairStaff[0].name}处理。`, '已指派']
    );

    successResponse(res, null, '维修请求指派成功');

  } catch (error) {
    console.error('指派维修请求错误:', error);
    errorResponse(res, '指派维修请求失败', 500, error);
  }
};
